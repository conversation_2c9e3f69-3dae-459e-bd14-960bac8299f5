/* 设置页面样式 */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 60rpx;
  height: 60rpx;
}

/* 设置列表 */
.settings-list {
  margin-top: calc(88rpx + var(--status-bar-height, 44rpx) + 20rpx);
  padding: 0 32rpx;
}

.setting-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 26rpx;
  color: #999;
  font-weight: 500;
  padding: 0 16rpx 20rpx;
  display: block;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  padding: 32rpx 24rpx;
  margin-bottom: 2rpx;
  transition: background 0.3s ease;
}

.setting-item:first-child {
  border-radius: 16rpx 16rpx 0 0;
}

.setting-item:last-child {
  border-radius: 0 0 16rpx 16rpx;
  margin-bottom: 0;
}

.setting-item:only-child {
  border-radius: 16rpx;
}

.setting-item:active {
  background: #f8f8f8;
}

.item-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.item-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.item-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.item-desc {
  font-size: 26rpx;
  color: #999;
}

/* 危险操作样式 */
.danger-item {
  justify-content: center;
}

.danger-text {
  color: #ff4444 !important;
}

/* 缓存管理弹窗 */
.cache-modal {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 0;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.cache-info {
  padding: 30rpx 40rpx;
}

.cache-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.cache-item:last-child {
  border-bottom: none;
  padding-top: 30rpx;
  margin-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.cache-label {
  font-size: 28rpx;
  color: #333;
}

.cache-size {
  font-size: 26rpx;
  color: #666;
}

.cache-size.total {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

.cache-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 30rpx 40rpx 0;
  border-top: 1rpx solid #eee;
}

/* 主题选择弹窗 */
.theme-modal {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 0;
}

.theme-options {
  padding: 20rpx 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background 0.3s ease;
}

.theme-option:active {
  background: #f8f8f8;
}

.theme-option.active {
  background: #f0f8f0;
}

.theme-preview {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #eee;
}

.theme-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.theme-option.active .theme-name {
  color: #4CAF50;
  font-weight: 500;
}

/* 语言选择弹窗 */
.language-modal {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 0;
}

.language-options {
  padding: 20rpx 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.language-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background 0.3s ease;
}

.language-option:active {
  background: #f8f8f8;
}

.language-option.active {
  background: #f0f8f0;
}

.language-name {
  font-size: 30rpx;
  color: #333;
}

.language-option.active .language-name {
  color: #4CAF50;
  font-weight: 500;
}
