<!--个人中心页面-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left"></view>
      <view class="navbar-title">个人中心</view>
      <view class="navbar-right" bindtap="showSettings">
        <van-icon name="setting-o" size="20" color="#333" />
      </view>
    </view>
  </view>

  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info" bindtap="editProfile">
      <image 
        class="user-avatar" 
        src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" 
        mode="aspectFill"
      />
      <view class="user-details">
        <text class="user-name">{{userInfo.nickName || '点击登录'}}</text>
        <text class="user-desc" wx:if="{{userInfo.nickName}}">{{userInfo.signature || '还没有个性签名'}}</text>
        <text class="login-tip" wx:else>登录后享受更多功能</text>
      </view>
      <van-icon name="arrow" size="16" color="#999" />
    </view>
    
    <!-- 用户统计 -->
    <view class="user-stats" wx:if="{{userInfo.nickName}}">
      <view class="stat-item" bindtap="goToFavorites">
        <text class="stat-number">{{stats.favorites || 0}}</text>
        <text class="stat-label">收藏</text>
      </view>
      <view class="stat-item" bindtap="goToHistory">
        <text class="stat-number">{{stats.history || 0}}</text>
        <text class="stat-label">历史</text>
      </view>
      <view class="stat-item" bindtap="goToRecipes">
        <text class="stat-number">{{stats.recipes || 0}}</text>
        <text class="stat-label">菜谱</text>
      </view>
      <view class="stat-item" bindtap="goToPoints">
        <text class="stat-number">{{stats.points || 0}}</text>
        <text class="stat-label">积分</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="goToFavorites">
        <view class="menu-icon">
          <van-icon name="star-o" size="20" color="#FF6B6B" />
        </view>
        <text class="menu-text">我的收藏</text>
        <view class="menu-extra">
          <text class="menu-count" wx:if="{{stats.favorites > 0}}">{{stats.favorites}}</text>
          <van-icon name="arrow" size="14" color="#999" />
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToHistory">
        <view class="menu-icon">
          <van-icon name="clock-o" size="20" color="#4ECDC4" />
        </view>
        <text class="menu-text">浏览历史</text>
        <view class="menu-extra">
          <text class="menu-count" wx:if="{{stats.history > 0}}">{{stats.history}}</text>
          <van-icon name="arrow" size="14" color="#999" />
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToShoppingList">
        <view class="menu-icon">
          <van-icon name="shopping-cart-o" size="20" color="#45B7D1" />
        </view>
        <text class="menu-text">购物清单</text>
        <view class="menu-extra">
          <van-icon name="arrow" size="14" color="#999" />
        </view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="goToRecipes">
        <view class="menu-icon">
          <van-icon name="bookmark-o" size="20" color="#96CEB4" />
        </view>
        <text class="menu-text">我的菜谱</text>
        <view class="menu-extra">
          <text class="menu-count" wx:if="{{stats.recipes > 0}}">{{stats.recipes}}</text>
          <van-icon name="arrow" size="14" color="#999" />
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToPoints">
        <view class="menu-icon">
          <van-icon name="gold-coin-o" size="20" color="#FECA57" />
        </view>
        <text class="menu-text">积分中心</text>
        <view class="menu-extra">
          <text class="menu-count" wx:if="{{stats.points > 0}}">{{stats.points}}</text>
          <van-icon name="arrow" size="14" color="#999" />
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToFeedback">
        <view class="menu-icon">
          <van-icon name="chat-o" size="20" color="#FF9FF3" />
        </view>
        <text class="menu-text">意见反馈</text>
        <view class="menu-extra">
          <van-icon name="arrow" size="14" color="#999" />
        </view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="goToSettings">
        <view class="menu-icon">
          <van-icon name="setting-o" size="20" color="#A8A8A8" />
        </view>
        <text class="menu-text">设置</text>
        <view class="menu-extra">
          <van-icon name="arrow" size="14" color="#999" />
        </view>
      </view>
      
      <view class="menu-item" bindtap="goToAbout">
        <view class="menu-icon">
          <van-icon name="info-o" size="20" color="#74B9FF" />
        </view>
        <text class="menu-text">关于我们</text>
        <view class="menu-extra">
          <van-icon name="arrow" size="14" color="#999" />
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions" wx:if="{{userInfo.nickName}}">
    <view class="section-title">快捷操作</view>
    <view class="actions-grid">
      <view class="action-item" bindtap="quickScan">
        <view class="action-icon">
          <van-icon name="photograph" size="24" color="#4CAF50" />
        </view>
        <text class="action-text">快速识别</text>
      </view>
      
      <view class="action-item" bindtap="randomRecipe">
        <view class="action-icon">
          <van-icon name="fire-o" size="24" color="#FF5722" />
        </view>
        <text class="action-text">随机菜谱</text>
      </view>
      
      <view class="action-item" bindtap="todayRecommend">
        <view class="action-icon">
          <van-icon name="star" size="24" color="#FFD700" />
        </view>
        <text class="action-text">今日推荐</text>
      </view>
      
      <view class="action-item" bindtap="shareApp">
        <view class="action-icon">
          <van-icon name="share-o" size="24" color="#2196F3" />
        </view>
        <text class="action-text">分享应用</text>
      </view>
    </view>
  </view>

  <!-- 最近活动 -->
  <view class="recent-activity" wx:if="{{recentActivities.length > 0}}">
    <view class="section-title">最近活动</view>
    <view class="activity-list">
      <view 
        class="activity-item" 
        wx:for="{{recentActivities}}" 
        wx:key="id"
        bindtap="viewActivity"
        data-activity="{{item}}"
      >
        <view class="activity-icon">
          <van-icon name="{{item.icon}}" size="16" color="{{item.color}}" />
        </view>
        <view class="activity-content">
          <text class="activity-text">{{item.text}}</text>
          <text class="activity-time">{{item.timeText}}</text>
        </view>
        <van-icon name="arrow" size="12" color="#ccc" />
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">AI智能菜谱 v{{version}}</text>
    <text class="copyright">© 2024 AI Recipe. All rights reserved.</text>
  </view>
</view>

<!-- 登录弹窗 -->
<van-popup 
  show="{{showLoginModal}}" 
  position="center" 
  round
  bind:close="hideLoginModal"
>
  <view class="login-modal">
    <view class="login-header">
      <image class="login-logo" src="/images/logo.png" mode="aspectFit" />
      <text class="login-title">欢迎使用AI智能菜谱</text>
      <text class="login-desc">登录后享受个性化服务</text>
    </view>
    
    <view class="login-content">
      <van-button 
        type="primary" 
        size="large" 
        icon="wechat"
        bindtap="wxLogin"
        loading="{{loginLoading}}"
      >
        {{loginLoading ? '登录中...' : '微信登录'}}
      </van-button>
      
      <view class="login-tips">
        <text>登录即表示同意</text>
        <text class="link" bindtap="showPrivacy">《隐私政策》</text>
        <text>和</text>
        <text class="link" bindtap="showTerms">《用户协议》</text>
      </view>
    </view>
  </view>
</van-popup>

<!-- 全局提示 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<van-notify id="van-notify" />
