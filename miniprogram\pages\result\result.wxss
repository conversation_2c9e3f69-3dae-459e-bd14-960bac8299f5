/* 识别结果页面样式 */

.container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.navbar-left text {
  font-size: 32rpx;
  color: #333;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 60rpx;
  display: flex;
  justify-content: flex-end;
}

/* 识别图片展示 */
.image-section {
  position: relative;
  margin-top: calc(88rpx + var(--status-bar-height, 44rpx));
  height: 400rpx;
  background: #000;
}

.result-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent 60%, rgba(0,0,0,0.3));
  display: flex;
  align-items: flex-end;
  padding: 30rpx;
}

.confidence-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(255,255,255,0.9);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.confidence-badge text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 内容区域 */
.content-section {
  padding: 40rpx 32rpx;
  padding-bottom: 120rpx;
}

/* 识别状态 */
.status-section {
  margin-bottom: 40rpx;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.status-item.success {
  background: #E8F5E8;
  color: #4CAF50;
}

.status-item.warning {
  background: #FFF3E0;
  color: #FF9800;
}

.status-item.error {
  background: #FFEBEE;
  color: #F44336;
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.ingredient-count,
.recipe-count {
  font-size: 24rpx;
  color: #666;
  margin-left: auto;
}

/* 食材列表 */
.ingredients-section {
  margin-bottom: 40rpx;
}

.ingredients-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.ingredient-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.ingredient-item:last-child {
  border-bottom: none;
}

.ingredient-item.selected {
  background: #f8fff8;
  border-left: 6rpx solid #4CAF50;
}

.ingredient-info {
  flex: 1;
}

.ingredient-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.ingredient-details {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.confidence {
  font-size: 24rpx;
  color: #666;
}

.category {
  font-size: 24rpx;
  color: #4CAF50;
  background: #E8F5E8;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.ingredient-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-input {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.unit {
  font-size: 24rpx;
  color: #666;
}

/* 营养信息 */
.nutrition-section {
  margin-bottom: 40rpx;
}

.nutrition-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  background: white;
  padding: 24rpx;
  border-radius: 16rpx;
}

.nutrition-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nutrition-icon {
  font-size: 32rpx;
  width: 48rpx;
  text-align: center;
}

.nutrition-info {
  flex: 1;
}

.nutrition-name {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.nutrition-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 推荐菜谱 */
.recipes-preview {
  margin-bottom: 40rpx;
}

.recipes-scroll {
  white-space: nowrap;
}

.recipes-list {
  display: flex;
  gap: 20rpx;
  padding: 0 4rpx;
}

.recipe-card {
  flex-shrink: 0;
  width: 240rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.recipe-image {
  width: 100%;
  height: 160rpx;
}

.recipe-info {
  padding: 20rpx;
}

.recipe-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cook-time,
.difficulty {
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮 */
.actions-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #eee;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 24rpx;
}

.action-buttons .van-button {
  flex: 1;
}

.extra-actions {
  display: flex;
  justify-content: space-around;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx;
}

.action-item text {
  font-size: 24rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40rpx;
}

/* 食材详情弹窗 */
.ingredient-detail-modal {
  padding: 40rpx;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.nutrition-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.nutrition-list .nutrition-item {
  font-size: 26rpx;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .nutrition-grid {
    grid-template-columns: 1fr;
  }
  
  .recipe-card {
    width: 200rpx;
  }
}

/* 动画效果 */
.ingredient-item {
  transition: all 0.3s ease;
}

.ingredient-item:active {
  transform: scale(0.98);
  background: #f8f8f8;
}

.recipe-card {
  transition: all 0.2s ease;
}

.recipe-card:active {
  transform: scale(0.95);
}

/* 加载状态 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}
