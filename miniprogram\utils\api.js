// API请求工具类
import Toast from '@vant/weapp/toast/toast';

// API基础配置
const API_CONFIG = {
  baseURL: 'http://localhost:3000', // 开发环境，生产环境需要修改
  timeout: 30000,
  retryCount: 3
};

/**
 * 网络请求封装
 */
class ApiService {
  constructor() {
    this.baseURL = API_CONFIG.baseURL;
    this.timeout = API_CONFIG.timeout;
    this.retryCount = API_CONFIG.retryCount;
  }

  /**
   * 通用请求方法
   * @param {Object} options 请求配置
   * @returns {Promise}
   */
  request(options) {
    return new Promise((resolve, reject) => {
      const {
        url,
        method = 'GET',
        data = {},
        header = {},
        showLoading = false,
        loadingText = '请求中...',
        showError = true,
        retry = 0
      } = options;

      // 显示加载提示
      if (showLoading) {
        wx.showLoading({
          title: loadingText,
          mask: true
        });
      }

      // 构建完整URL
      const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;

      // 获取token
      const token = wx.getStorageSync('token');
      if (token) {
        header['Authorization'] = `Bearer ${token}`;
      }

      // 设置默认header
      header['Content-Type'] = header['Content-Type'] || 'application/json';

      wx.request({
        url: fullUrl,
        method,
        data,
        header,
        timeout: this.timeout,
        success: (res) => {
          if (showLoading) {
            wx.hideLoading();
          }

          // 处理HTTP状态码
          if (res.statusCode >= 200 && res.statusCode < 300) {
            // 处理业务状态码
            if (res.data && res.data.success !== undefined) {
              if (res.data.success) {
                resolve(res.data);
              } else {
                const error = new Error(res.data.message || '请求失败');
                error.code = res.data.code;
                error.data = res.data;
                reject(error);
              }
            } else {
              resolve(res.data);
            }
          } else if (res.statusCode === 401) {
            // token过期，清除本地存储并跳转登录
            wx.removeStorageSync('token');
            wx.removeStorageSync('userInfo');
            wx.reLaunch({
              url: '/pages/index/index'
            });
            reject(new Error('登录已过期，请重新登录'));
          } else {
            const error = new Error(`请求失败 (${res.statusCode})`);
            error.statusCode = res.statusCode;
            reject(error);
          }
        },
        fail: (err) => {
          if (showLoading) {
            wx.hideLoading();
          }

          console.error('Request failed:', err);

          // 网络错误重试
          if (retry < this.retryCount && this.shouldRetry(err)) {
            console.log(`Retrying request (${retry + 1}/${this.retryCount})`);
            setTimeout(() => {
              this.request({ ...options, retry: retry + 1 })
                .then(resolve)
                .catch(reject);
            }, 1000 * (retry + 1));
            return;
          }

          const error = new Error(this.getErrorMessage(err));
          error.originalError = err;
          reject(error);
        },
        complete: () => {
          if (showLoading) {
            wx.hideLoading();
          }
        }
      });
    }).catch(error => {
      // 统一错误处理
      if (showError) {
        Toast.fail(error.message || '网络请求失败');
      }
      throw error;
    });
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error) {
    // 网络错误或超时错误可以重试
    return error.errMsg && (
      error.errMsg.includes('timeout') ||
      error.errMsg.includes('fail') ||
      error.errMsg.includes('network')
    );
  }

  /**
   * 获取错误信息
   */
  getErrorMessage(error) {
    if (error.errMsg) {
      if (error.errMsg.includes('timeout')) {
        return '请求超时，请检查网络连接';
      } else if (error.errMsg.includes('network')) {
        return '网络连接失败，请检查网络设置';
      } else if (error.errMsg.includes('fail')) {
        return '请求失败，请稍后重试';
      }
    }
    return '网络请求失败';
  }

  /**
   * GET请求
   */
  get(url, params = {}, options = {}) {
    // 将参数拼接到URL
    if (Object.keys(params).length > 0) {
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');
      url += (url.includes('?') ? '&' : '?') + queryString;
    }

    return this.request({
      url,
      method: 'GET',
      ...options
    });
  }

  /**
   * POST请求
   */
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    });
  }

  /**
   * PUT请求
   */
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  }

  /**
   * DELETE请求
   */
  delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    });
  }

  /**
   * 文件上传
   */
  upload(url, filePath, options = {}) {
    return new Promise((resolve, reject) => {
      const {
        name = 'file',
        formData = {},
        header = {},
        showLoading = true,
        loadingText = '上传中...'
      } = options;

      if (showLoading) {
        wx.showLoading({
          title: loadingText,
          mask: true
        });
      }

      // 获取token
      const token = wx.getStorageSync('token');
      if (token) {
        header['Authorization'] = `Bearer ${token}`;
      }

      // 构建完整URL
      const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;

      wx.uploadFile({
        url: fullUrl,
        filePath,
        name,
        formData,
        header,
        success: (res) => {
          if (showLoading) {
            wx.hideLoading();
          }

          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data);
            } else {
              reject(new Error(data.message || '上传失败'));
            }
          } catch (e) {
            reject(new Error('响应数据解析失败'));
          }
        },
        fail: (err) => {
          if (showLoading) {
            wx.hideLoading();
          }
          reject(new Error(this.getErrorMessage(err)));
        }
      });
    });
  }
}

// 创建实例
const apiService = new ApiService();

// 导出常用方法
export const request = apiService.request.bind(apiService);
export const get = apiService.get.bind(apiService);
export const post = apiService.post.bind(apiService);
export const put = apiService.put.bind(apiService);
export const del = apiService.delete.bind(apiService);
export const upload = apiService.upload.bind(apiService);

export default apiService;
