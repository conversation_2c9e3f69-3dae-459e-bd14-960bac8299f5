// 识别结果页面逻辑
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import Notify from '@vant/weapp/notify/notify';
import { post } from '../../utils/api';
import { formatNutrition, formatCookTime, formatDifficulty } from '../../utils/format';
import { setStorage, getStorage, CACHE_KEYS } from '../../utils/storage';

Page({
  data: {
    // 识别结果数据
    originalImage: '', // 原始图片
    confidence: 0, // 整体置信度
    recognitionStatus: 'success', // success, partial, failed
    ingredients: [], // 识别到的食材
    nutritionInfo: [], // 营养信息
    recommendedRecipes: [], // 推荐菜谱
    
    // UI状态
    showMenuModal: false,
    showIngredientDetail: false,
    selectedIngredient: null,
    generating: false,
    
    // 计算属性
    hasSelectedIngredients: false,
    
    // 菜单选项
    menuActions: [
      { name: '保存结果', value: 'save' },
      { name: '分享结果', value: 'share' },
      { name: '重新识别', value: 'retake' },
      { name: '意见反馈', value: 'feedback' }
    ]
  },

  onLoad(options) {
    console.log('Result page loaded with options:', options);
    
    // 获取识别结果数据
    if (options.data) {
      try {
        const resultData = JSON.parse(decodeURIComponent(options.data));
        this.processRecognitionResult(resultData);
      } catch (error) {
        console.error('Parse result data failed:', error);
        this.showErrorAndGoBack('数据解析失败');
      }
    } else {
      this.showErrorAndGoBack('未找到识别结果');
    }
  },

  onShow() {
    // 页面显示时更新状态
    this.updateSelectedIngredientsStatus();
  },

  // 处理识别结果数据
  processRecognitionResult(resultData) {
    console.log('Processing recognition result:', resultData);
    
    const {
      image,
      confidence,
      ingredients = [],
      nutrition,
      recommendedRecipes = []
    } = resultData;

    // 处理食材数据
    const processedIngredients = ingredients.map((ingredient, index) => ({
      id: ingredient.id || `ingredient_${index}`,
      name: ingredient.name,
      confidence: Math.round(ingredient.confidence || 0),
      category: ingredient.category,
      quantity: ingredient.quantity || 1,
      unit: ingredient.unit || '个',
      selected: true, // 默认选中
      nutrition: ingredient.nutrition
    }));

    // 处理营养信息
    const nutritionInfo = nutrition ? formatNutrition(nutrition) : [];

    // 确定识别状态
    let recognitionStatus = 'success';
    if (processedIngredients.length === 0) {
      recognitionStatus = 'failed';
    } else if (processedIngredients.some(item => item.confidence < 60)) {
      recognitionStatus = 'partial';
    }

    this.setData({
      originalImage: image,
      confidence: Math.round(confidence || 0),
      recognitionStatus,
      ingredients: processedIngredients,
      nutritionInfo,
      recommendedRecipes: recommendedRecipes.slice(0, 5), // 最多显示5个推荐
      hasSelectedIngredients: processedIngredients.length > 0
    });

    // 保存识别历史
    this.saveRecognitionHistory(resultData);
  },

  // 显示错误并返回
  showErrorAndGoBack(message) {
    Toast.fail(message);
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },

  // 保存识别历史
  saveRecognitionHistory(resultData) {
    try {
      const history = getStorage(CACHE_KEYS.RECOGNITION_HISTORY, []);
      const newRecord = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        image: resultData.image,
        ingredients: resultData.ingredients,
        confidence: resultData.confidence
      };
      
      history.unshift(newRecord);
      // 只保留最近50条记录
      if (history.length > 50) {
        history.splice(50);
      }
      
      setStorage(CACHE_KEYS.RECOGNITION_HISTORY, history, 30 * 24 * 60 * 60); // 30天过期
    } catch (error) {
      console.error('Save recognition history failed:', error);
    }
  },

  // 切换食材选择状态
  toggleIngredient(e) {
    const { index } = e.currentTarget.dataset;
    const ingredients = [...this.data.ingredients];
    ingredients[index].selected = !ingredients[index].selected;
    
    this.setData({ ingredients });
    this.updateSelectedIngredientsStatus();
  },

  // 更新选中食材状态
  updateSelectedIngredientsStatus() {
    const hasSelected = this.data.ingredients.some(item => item.selected);
    this.setData({ hasSelectedIngredients: hasSelected });
  },

  // 修改食材数量
  onQuantityChange(e) {
    const { index } = e.currentTarget.dataset;
    const { detail } = e;
    const ingredients = [...this.data.ingredients];
    ingredients[index].quantity = detail;
    
    this.setData({ ingredients });
  },

  // 预览图片
  previewImage() {
    wx.previewImage({
      urls: [this.data.originalImage],
      current: this.data.originalImage
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 显示菜单
  showMenu() {
    this.setData({ showMenuModal: true });
  },

  // 隐藏菜单
  hideMenu() {
    this.setData({ showMenuModal: false });
  },

  // 菜单选择
  onMenuSelect(e) {
    const { value } = e.detail;
    this.hideMenu();
    
    switch (value) {
      case 'save':
        this.saveResult();
        break;
      case 'share':
        this.shareResult();
        break;
      case 'retake':
        this.retakePhoto();
        break;
      case 'feedback':
        this.showFeedback();
        break;
    }
  },

  // 重新拍照
  retakePhoto() {
    wx.navigateBack();
  },

  // 生成菜谱
  async generateRecipes() {
    if (!this.data.hasSelectedIngredients) {
      Toast.fail('请至少选择一种食材');
      return;
    }

    this.setData({ generating: true });

    try {
      // 获取选中的食材
      const selectedIngredients = this.data.ingredients
        .filter(item => item.selected)
        .map(item => ({
          name: item.name,
          quantity: item.quantity,
          unit: item.unit
        }));

      // 调用生成菜谱API
      const result = await post('/api/ai/generate-recipes', {
        ingredients: selectedIngredients,
        preferences: this.getUserPreferences()
      }, {
        showLoading: false,
        loadingText: '生成菜谱中...'
      });

      if (result.success) {
        // 跳转到菜谱页面
        wx.navigateTo({
          url: `/pages/recipe/recipe?data=${encodeURIComponent(JSON.stringify(result.data))}`
        });
      } else {
        throw new Error(result.message || '生成菜谱失败');
      }
    } catch (error) {
      console.error('Generate recipes failed:', error);
      Toast.fail(error.message || '生成菜谱失败，请重试');
    } finally {
      this.setData({ generating: false });
    }
  },

  // 获取用户偏好
  getUserPreferences() {
    // 这里可以从用户设置中获取偏好
    return {
      difficulty: 'medium',
      cookTime: 30,
      cuisine: [],
      dietary: []
    };
  },

  // 查看菜谱详情
  viewRecipe(e) {
    const { recipe } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/recipe/detail/detail?id=${recipe.id}`
    });
  },

  // 保存结果
  async saveResult() {
    try {
      const result = await post('/api/user/save-recognition', {
        image: this.data.originalImage,
        ingredients: this.data.ingredients,
        confidence: this.data.confidence
      });

      if (result.success) {
        Toast.success('保存成功');
      } else {
        throw new Error(result.message || '保存失败');
      }
    } catch (error) {
      console.error('Save result failed:', error);
      Toast.fail(error.message || '保存失败');
    }
  },

  // 分享结果
  shareResult() {
    const selectedIngredients = this.data.ingredients
      .filter(item => item.selected)
      .map(item => item.name)
      .join('、');

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 设置分享内容
    wx.onShareAppMessage(() => ({
      title: `我识别到了${selectedIngredients}，快来看看能做什么菜！`,
      path: '/pages/index/index',
      imageUrl: this.data.originalImage
    }));
  },

  // 添加到购物清单
  addToShoppingList() {
    const selectedIngredients = this.data.ingredients.filter(item => item.selected);
    
    if (selectedIngredients.length === 0) {
      Toast.fail('请先选择食材');
      return;
    }

    // 这里可以实现添加到购物清单的逻辑
    Toast.success(`已添加${selectedIngredients.length}种食材到购物清单`);
  },

  // 显示食材详情
  showIngredientDetail(e) {
    const { ingredient } = e.currentTarget.dataset;
    this.setData({
      selectedIngredient: ingredient,
      showIngredientDetail: true
    });
  },

  // 隐藏食材详情
  hideIngredientDetail() {
    this.setData({
      showIngredientDetail: false,
      selectedIngredient: null
    });
  },

  // 显示反馈
  showFeedback() {
    Dialog.confirm({
      title: '意见反馈',
      message: '识别结果不准确？请告诉我们！',
      confirmButtonText: '去反馈',
      cancelButtonText: '取消'
    }).then(() => {
      // 跳转到反馈页面或显示反馈表单
      Toast('感谢您的反馈！');
    }).catch(() => {
      // 用户取消
    });
  }
});
