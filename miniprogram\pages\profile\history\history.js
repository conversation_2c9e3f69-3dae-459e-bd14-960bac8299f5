// 历史记录页面逻辑
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import Notify from '@vant/weapp/notify/notify';
import { get, post, del } from '../../../utils/api';
import { getStorage, setStorage, CACHE_KEYS } from '../../../utils/storage';
import { formatTime } from '../../../utils/format';

Page({
  data: {
    // 历史记录数据
    historyList: [],
    displayList: [],
    groupedList: [],
    
    // UI状态
    loading: false,
    editMode: false,
    showSearch: false,
    showFilter: false,
    showFilterModal: false,
    
    // 搜索相关
    searchKeyword: '',
    
    // 筛选相关
    filterType: 'all',
    timeRange: 'all',
    filterOptions: [
      { label: '全部', value: 'all' },
      { label: '食材识别', value: 'recognition' },
      { label: '菜谱生成', value: 'recipe' },
      { label: '收藏操作', value: 'favorite' },
      { label: '分享记录', value: 'share' }
    ],
    timeRangeOptions: [
      { label: '全部时间', value: 'all' },
      { label: '今天', value: 'today' },
      { label: '最近7天', value: 'week' },
      { label: '最近30天', value: 'month' }
    ],
    
    // 选择相关
    selectedCount: 0,
    allSelected: false
  },

  onLoad() {
    console.log('History page loaded');
    this.loadHistory();
  },

  onShow() {
    // 每次显示时刷新数据
    this.loadHistory();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 加载历史记录
  async loadHistory() {
    try {
      this.setData({ loading: true });
      
      // 先从本地缓存获取
      const cachedHistory = getStorage(CACHE_KEYS.USER_HISTORY, []);
      if (cachedHistory.length > 0) {
        this.processHistoryList(cachedHistory);
      }
      
      // 从服务器获取最新数据
      const response = await get('/api/users/history');
      if (response.success) {
        const historyList = response.data.map(item => ({
          ...item,
          selected: false,
          timeText: formatTime(item.createdAt, 'HH:mm'),
          typeIcon: this.getTypeIcon(item.type),
          typeColor: this.getTypeColor(item.type),
          title: this.getRecordTitle(item),
          description: this.getRecordDescription(item)
        }));
        
        // 缓存到本地
        setStorage(CACHE_KEYS.USER_HISTORY, historyList);
        this.processHistoryList(historyList);
      }
    } catch (error) {
      console.error('Load history failed:', error);
      Toast.fail('加载失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 处理历史记录数据
  processHistoryList(historyList) {
    const displayList = this.filterAndSortList(historyList);
    const groupedList = this.groupByDate(displayList);
    
    this.setData({ 
      historyList,
      displayList,
      groupedList
    });
  },

  // 过滤和排序列表
  filterAndSortList(list) {
    let filteredList = [...list];
    
    // 搜索过滤
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filteredList = filteredList.filter(item => 
        item.title.toLowerCase().includes(keyword) ||
        item.description.toLowerCase().includes(keyword)
      );
    }
    
    // 类型过滤
    if (this.data.filterType !== 'all') {
      filteredList = filteredList.filter(item => item.type === this.data.filterType);
    }
    
    // 时间范围过滤
    if (this.data.timeRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (this.data.timeRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setDate(now.getDate() - 30);
          break;
      }
      
      filteredList = filteredList.filter(item => 
        new Date(item.createdAt) >= filterDate
      );
    }
    
    // 按时间倒序排列
    filteredList.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    return filteredList;
  },

  // 按日期分组
  groupByDate(list) {
    const groups = {};
    const now = new Date();
    
    list.forEach(item => {
      const date = new Date(item.createdAt);
      const dateKey = formatTime(date, 'YYYY-MM-DD');
      
      if (!groups[dateKey]) {
        groups[dateKey] = {
          date: dateKey,
          dateText: this.getDateText(date, now),
          records: []
        };
      }
      
      groups[dateKey].records.push(item);
    });
    
    return Object.values(groups).sort((a, b) => new Date(b.date) - new Date(a.date));
  },

  // 获取日期显示文本
  getDateText(date, now) {
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    
    if (itemDate.getTime() === today.getTime()) {
      return '今天';
    } else if (itemDate.getTime() === yesterday.getTime()) {
      return '昨天';
    } else {
      return formatTime(date, 'MM月DD日');
    }
  },

  // 获取记录类型图标
  getTypeIcon(type) {
    const iconMap = {
      recognition: 'photo-o',
      recipe: 'bookmark-o',
      favorite: 'like-o',
      share: 'share-o'
    };
    return iconMap[type] || 'records';
  },

  // 获取记录类型颜色
  getTypeColor(type) {
    const colorMap = {
      recognition: '#4CAF50',
      recipe: '#2196F3',
      favorite: '#FF9800',
      share: '#9C27B0'
    };
    return colorMap[type] || '#666';
  },

  // 获取记录标题
  getRecordTitle(item) {
    const titleMap = {
      recognition: '食材识别',
      recipe: '菜谱生成',
      favorite: '收藏菜谱',
      share: '分享菜谱'
    };
    return titleMap[item.type] || '未知操作';
  },

  // 获取记录描述
  getRecordDescription(item) {
    switch (item.type) {
      case 'recognition':
        return `识别了 ${item.data?.ingredients?.length || 0} 种食材`;
      case 'recipe':
        return `生成了菜谱：${item.data?.recipeName || '未知菜谱'}`;
      case 'favorite':
        return `收藏了：${item.data?.recipeName || '未知菜谱'}`;
      case 'share':
        return `分享了：${item.data?.recipeName || '未知菜谱'}`;
      default:
        return '详细信息不可用';
    }
  },

  // 切换编辑模式
  toggleEditMode() {
    const editMode = !this.data.editMode;
    this.setData({ 
      editMode,
      selectedCount: 0,
      allSelected: false
    });
    
    // 重置选择状态
    if (!editMode) {
      const historyList = this.data.historyList.map(item => ({
        ...item,
        selected: false
      }));
      this.processHistoryList(historyList);
    }
  },

  // 显示搜索栏
  searchHistory() {
    this.setData({ showSearch: true });
  },

  // 隐藏搜索栏
  hideSearch() {
    this.setData({ 
      showSearch: false,
      searchKeyword: ''
    });
    this.refreshDisplayList();
  },

  // 搜索输入变化
  onSearchChange(e) {
    this.setData({ searchKeyword: e.detail });
    this.refreshDisplayList();
  },

  // 执行搜索
  onSearch(e) {
    this.setData({ searchKeyword: e.detail });
    this.refreshDisplayList();
  },

  // 显示筛选
  filterHistory() {
    this.setData({ showFilterModal: true });
  },

  // 隐藏筛选弹窗
  hideFilterModal() {
    this.setData({ showFilterModal: false });
  },

  // 选择筛选类型
  selectFilter(e) {
    const filterType = e.currentTarget.dataset.type;
    this.setData({ filterType });
  },

  // 选择时间范围
  selectTimeRange(e) {
    const timeRange = e.currentTarget.dataset.range;
    this.setData({ timeRange });
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      filterType: 'all',
      timeRange: 'all'
    });
  },

  // 应用筛选
  applyFilter() {
    this.setData({ 
      showFilterModal: false,
      showFilter: this.data.filterType !== 'all' || this.data.timeRange !== 'all'
    });
    this.refreshDisplayList();
  },

  // 刷新显示列表
  refreshDisplayList() {
    const displayList = this.filterAndSortList(this.data.historyList);
    const groupedList = this.groupByDate(displayList);
    this.setData({ displayList, groupedList });
  },

  // 查看记录详情
  viewRecord(e) {
    if (this.data.editMode) return;
    
    const record = e.currentTarget.dataset.record;
    
    // 根据记录类型跳转到不同页面
    switch (record.type) {
      case 'recognition':
        if (record.data?.resultId) {
          wx.navigateTo({
            url: `/pages/result/result?id=${record.data.resultId}`
          });
        }
        break;
      case 'recipe':
      case 'favorite':
        if (record.data?.recipeId) {
          wx.navigateTo({
            url: `/pages/recipe/detail/detail?id=${record.data.recipeId}`
          });
        }
        break;
      default:
        Toast('暂不支持查看此类型记录');
    }
  },

  // 切换选择状态
  toggleSelect(e) {
    e.stopPropagation();
    const id = e.currentTarget.dataset.id;
    
    const historyList = this.data.historyList.map(item => {
      if (item.id === id) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    
    const selectedCount = historyList.filter(item => item.selected).length;
    const allSelected = selectedCount === historyList.length;
    
    this.setData({ 
      historyList,
      selectedCount,
      allSelected
    });
    this.refreshDisplayList();
  },

  // 全选/取消全选
  toggleSelectAll() {
    const allSelected = !this.data.allSelected;
    const historyList = this.data.historyList.map(item => ({
      ...item,
      selected: allSelected
    }));
    
    const selectedCount = allSelected ? historyList.length : 0;
    
    this.setData({ 
      historyList,
      selectedCount,
      allSelected
    });
    this.refreshDisplayList();
  },

  // 分享记录
  shareRecord(e) {
    e.stopPropagation();
    const record = e.currentTarget.dataset.record;
    
    // 这里可以添加分享逻辑
    Toast.success('分享功能开发中');
  },

  // 删除记录
  async deleteRecord(e) {
    e.stopPropagation();
    const id = e.currentTarget.dataset.id;
    
    try {
      const result = await Dialog.confirm({
        title: '确认删除',
        message: '确定要删除这条记录吗？'
      });
      
      if (result) {
        await this.deleteHistoryById(id);
      }
    } catch (error) {
      // 用户取消
    }
  },

  // 批量导出
  batchExport() {
    if (this.data.selectedCount === 0) return;
    
    Toast.success('导出功能开发中');
  },

  // 批量删除
  async batchDelete() {
    if (this.data.selectedCount === 0) return;
    
    try {
      const result = await Dialog.confirm({
        title: '确认删除',
        message: `确定要删除选中的 ${this.data.selectedCount} 条记录吗？`
      });
      
      if (result) {
        const selectedIds = this.data.historyList
          .filter(item => item.selected)
          .map(item => item.id);
        
        await this.deleteHistoryByIds(selectedIds);
      }
    } catch (error) {
      // 用户取消
    }
  },

  // 删除单条历史记录
  async deleteHistoryById(id) {
    try {
      const response = await del(`/api/users/history/${id}`);
      if (response.success) {
        const historyList = this.data.historyList.filter(item => item.id !== id);
        setStorage(CACHE_KEYS.USER_HISTORY, historyList);
        this.processHistoryList(historyList);
        Toast.success('已删除记录');
      }
    } catch (error) {
      console.error('Delete history failed:', error);
      Toast.fail('删除失败，请重试');
    }
  },

  // 批量删除历史记录
  async deleteHistoryByIds(ids) {
    try {
      const response = await post('/api/users/history/batch-delete', { ids });
      if (response.success) {
        const historyList = this.data.historyList.filter(item => !ids.includes(item.id));
        setStorage(CACHE_KEYS.USER_HISTORY, historyList);
        this.processHistoryList(historyList);
        this.setData({ 
          editMode: false,
          selectedCount: 0,
          allSelected: false
        });
        Toast.success(`已删除 ${ids.length} 条记录`);
      }
    } catch (error) {
      console.error('Batch delete history failed:', error);
      Toast.fail('删除失败，请重试');
    }
  },

  // 去拍照页面
  goCamera() {
    wx.navigateTo({
      url: '/pages/camera/camera'
    });
  }
});
