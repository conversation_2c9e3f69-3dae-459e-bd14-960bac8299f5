// 个人中心页面逻辑
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import Notify from '@vant/weapp/notify/notify';
import { get, post } from '../../utils/api';
import { getStorage, setStorage, removeStorage, CACHE_KEYS } from '../../utils/storage';
import { formatTime } from '../../utils/format';

Page({
  data: {
    // 用户信息
    userInfo: {},
    stats: {
      favorites: 0,
      history: 0,
      recipes: 0,
      points: 0
    },
    
    // 最近活动
    recentActivities: [],
    
    // UI状态
    showLoginModal: false,
    loginLoading: false,
    
    // 应用信息
    version: '1.0.0'
  },

  onLoad() {
    console.log('Profile page loaded');
    this.loadUserData();
  },

  onShow() {
    // 每次显示时刷新数据
    this.loadUserData();
    this.loadUserStats();
    this.loadRecentActivities();
  },

  // 加载用户数据
  loadUserData() {
    try {
      // 从本地存储获取用户信息
      const userInfo = getStorage(CACHE_KEYS.USER_INFO, {});
      
      this.setData({ userInfo });
      
      // 如果用户已登录，从服务器同步最新信息
      if (userInfo.openid) {
        this.syncUserInfo();
      }
    } catch (error) {
      console.error('Load user data failed:', error);
    }
  },

  // 同步用户信息
  async syncUserInfo() {
    try {
      const result = await get('/api/user/profile');
      
      if (result.success) {
        const userInfo = {
          ...this.data.userInfo,
          ...result.data
        };
        
        this.setData({ userInfo });
        setStorage(CACHE_KEYS.USER_INFO, userInfo, 7 * 24 * 60 * 60); // 7天过期
      }
    } catch (error) {
      console.error('Sync user info failed:', error);
      // 同步失败不影响页面显示
    }
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      // 从本地存储获取基础统计
      const favorites = getStorage(CACHE_KEYS.FAVORITE_RECIPES, []);
      const history = getStorage(CACHE_KEYS.VIEW_HISTORY, []);
      
      let stats = {
        favorites: favorites.length,
        history: history.length,
        recipes: 0,
        points: 0
      };

      // 如果用户已登录，从服务器获取完整统计
      if (this.data.userInfo.openid) {
        const result = await get('/api/user/stats');
        
        if (result.success) {
          stats = {
            ...stats,
            ...result.data
          };
        }
      }

      this.setData({ stats });
    } catch (error) {
      console.error('Load user stats failed:', error);
    }
  },

  // 加载最近活动
  loadRecentActivities() {
    try {
      const history = getStorage(CACHE_KEYS.VIEW_HISTORY, []);
      const activities = [];

      // 处理浏览历史
      history.slice(0, 5).forEach(item => {
        let activityItem = {
          id: item.id,
          icon: 'clock-o',
          color: '#4ECDC4',
          timeText: formatTime(item.timestamp)
        };

        switch (item.type) {
          case 'recipe_detail':
            activityItem.text = `浏览了菜谱《${item.data.recipeName}》`;
            activityItem.icon = 'bookmark-o';
            activityItem.color = '#96CEB4';
            break;
          case 'ingredient_scan':
            activityItem.text = `识别了食材`;
            activityItem.icon = 'photograph';
            activityItem.color = '#4CAF50';
            break;
          case 'recipe_favorite':
            activityItem.text = `收藏了菜谱`;
            activityItem.icon = 'star';
            activityItem.color = '#FFD700';
            break;
          default:
            activityItem.text = '进行了操作';
        }

        activities.push(activityItem);
      });

      this.setData({ recentActivities: activities });
    } catch (error) {
      console.error('Load recent activities failed:', error);
    }
  },

  // 编辑个人资料
  editProfile() {
    if (!this.data.userInfo.nickName) {
      this.showLoginModal();
      return;
    }

    // 跳转到编辑资料页面
    wx.navigateTo({
      url: '/pages/profile/edit/edit'
    });
  },

  // 显示登录弹窗
  showLoginModal() {
    this.setData({ showLoginModal: true });
  },

  // 隐藏登录弹窗
  hideLoginModal() {
    this.setData({ showLoginModal: false });
  },

  // 微信登录
  async wxLogin() {
    if (this.data.loginLoading) return;

    this.setData({ loginLoading: true });

    try {
      // 获取微信登录凭证
      const loginResult = await wx.login();
      
      if (!loginResult.code) {
        throw new Error('获取登录凭证失败');
      }

      // 获取用户信息授权
      const userProfile = await wx.getUserProfile({
        desc: '用于完善用户资料'
      });

      // 调用后端登录接口
      const result = await post('/api/auth/wx-login', {
        code: loginResult.code,
        userInfo: userProfile.userInfo
      });

      if (result.success) {
        // 保存用户信息和token
        const userInfo = result.data.userInfo;
        const token = result.data.token;

        setStorage(CACHE_KEYS.USER_INFO, userInfo, 7 * 24 * 60 * 60);
        setStorage(CACHE_KEYS.AUTH_TOKEN, token, 7 * 24 * 60 * 60);

        this.setData({ 
          userInfo,
          showLoginModal: false 
        });

        // 重新加载统计数据
        this.loadUserStats();
        
        Toast.success('登录成功');
      } else {
        throw new Error(result.message || '登录失败');
      }
    } catch (error) {
      console.error('WeChat login failed:', error);
      
      if (error.errMsg && error.errMsg.includes('getUserProfile:fail auth deny')) {
        Toast.fail('需要授权才能登录');
      } else {
        Toast.fail(error.message || '登录失败');
      }
    } finally {
      this.setData({ loginLoading: false });
    }
  },

  // 跳转到收藏页面
  goToFavorites() {
    wx.navigateTo({
      url: '/pages/profile/favorites/favorites'
    });
  },

  // 跳转到历史页面
  goToHistory() {
    wx.navigateTo({
      url: '/pages/profile/history/history'
    });
  },

  // 跳转到购物清单
  goToShoppingList() {
    wx.navigateTo({
      url: '/pages/profile/shopping/shopping'
    });
  },

  // 跳转到我的菜谱
  goToRecipes() {
    if (!this.data.userInfo.nickName) {
      this.showLoginModal();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/profile/recipes/recipes'
    });
  },

  // 跳转到积分中心
  goToPoints() {
    if (!this.data.userInfo.nickName) {
      this.showLoginModal();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/profile/points/points'
    });
  },

  // 跳转到意见反馈
  goToFeedback() {
    wx.navigateTo({
      url: '/pages/profile/feedback/feedback'
    });
  },

  // 跳转到设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/profile/settings/settings'
    });
  },

  // 显示设置
  showSettings() {
    this.goToSettings();
  },

  // 跳转到关于我们
  goToAbout() {
    wx.navigateTo({
      url: '/pages/profile/about/about'
    });
  },

  // 快速扫描
  quickScan() {
    wx.switchTab({
      url: '/pages/camera/camera'
    });
  },

  // 随机菜谱
  async randomRecipe() {
    try {
      wx.showLoading({ title: '获取中...' });
      
      const result = await get('/api/recipes/random');
      
      if (result.success && result.data) {
        wx.navigateTo({
          url: `/pages/recipe/detail/detail?id=${result.data.id}`
        });
      } else {
        Toast.fail('获取随机菜谱失败');
      }
    } catch (error) {
      console.error('Get random recipe failed:', error);
      Toast.fail('获取随机菜谱失败');
    } finally {
      wx.hideLoading();
    }
  },

  // 今日推荐
  todayRecommend() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 分享应用
  shareApp() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    Toast.success('请点击右上角分享');
  },

  // 查看活动详情
  viewActivity(e) {
    const { activity } = e.currentTarget.dataset;
    
    // 根据活动类型跳转到相应页面
    if (activity.type === 'recipe_detail' && activity.data.recipeId) {
      wx.navigateTo({
        url: `/pages/recipe/detail/detail?id=${activity.data.recipeId}`
      });
    }
  },

  // 显示隐私政策
  showPrivacy() {
    wx.navigateTo({
      url: '/pages/profile/privacy/privacy'
    });
  },

  // 显示用户协议
  showTerms() {
    wx.navigateTo({
      url: '/pages/profile/terms/terms'
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: 'AI智能菜谱 - 拍照识别食材，智能生成菜谱',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: 'AI智能菜谱 - 拍照识别食材，智能生成菜谱',
      imageUrl: '/images/share-cover.png'
    };
  }
});
