// 图片处理工具类

/**
 * 图片压缩配置
 */
const COMPRESS_CONFIG = {
  // 最大宽度
  maxWidth: 1200,
  // 最大高度  
  maxHeight: 1200,
  // 压缩质量 (0-1)
  quality: 0.8,
  // 最大文件大小 (KB)
  maxSize: 500
};

/**
 * 图片工具类
 */
class ImageUtils {
  
  /**
   * 压缩图片
   * @param {string} imagePath 图片路径
   * @param {Object} options 压缩选项
   * @returns {Promise<string>} 压缩后的图片路径
   */
  compressImage(imagePath, options = {}) {
    return new Promise((resolve, reject) => {
      const config = { ...COMPRESS_CONFIG, ...options };
      
      // 获取图片信息
      wx.getImageInfo({
        src: imagePath,
        success: (imageInfo) => {
          const { width, height } = imageInfo;
          
          // 计算压缩后的尺寸
          const { newWidth, newHeight } = this.calculateCompressSize(width, height, config);
          
          // 创建canvas进行压缩
          this.compressWithCanvas(imagePath, newWidth, newHeight, config.quality)
            .then(resolve)
            .catch(reject);
        },
        fail: reject
      });
    });
  }

  /**
   * 计算压缩后的尺寸
   */
  calculateCompressSize(width, height, config) {
    let newWidth = width;
    let newHeight = height;
    
    // 按比例缩放
    if (width > config.maxWidth || height > config.maxHeight) {
      const widthRatio = config.maxWidth / width;
      const heightRatio = config.maxHeight / height;
      const ratio = Math.min(widthRatio, heightRatio);
      
      newWidth = Math.floor(width * ratio);
      newHeight = Math.floor(height * ratio);
    }
    
    return { newWidth, newHeight };
  }

  /**
   * 使用Canvas压缩图片
   */
  compressWithCanvas(imagePath, width, height, quality) {
    return new Promise((resolve, reject) => {
      const canvas = wx.createOffscreenCanvas({ type: '2d', width, height });
      const ctx = canvas.getContext('2d');
      
      // 创建图片对象
      const img = canvas.createImage();
      
      img.onload = () => {
        // 清空画布
        ctx.clearRect(0, 0, width, height);
        
        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, width, height);
        
        // 导出为临时文件
        wx.canvasToTempFilePath({
          canvas,
          quality,
          fileType: 'jpg',
          success: (res) => {
            resolve(res.tempFilePath);
          },
          fail: reject
        });
      };
      
      img.onerror = reject;
      img.src = imagePath;
    });
  }

  /**
   * 获取图片文件大小
   * @param {string} imagePath 图片路径
   * @returns {Promise<number>} 文件大小(KB)
   */
  getImageSize(imagePath) {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath: imagePath,
        success: (res) => {
          resolve(Math.round(res.size / 1024)); // 转换为KB
        },
        fail: reject
      });
    });
  }

  /**
   * 智能压缩图片（根据文件大小自动调整压缩参数）
   * @param {string} imagePath 图片路径
   * @param {number} targetSize 目标大小(KB)
   * @returns {Promise<string>} 压缩后的图片路径
   */
  smartCompress(imagePath, targetSize = 500) {
    return new Promise(async (resolve, reject) => {
      try {
        // 获取原始文件大小
        const originalSize = await this.getImageSize(imagePath);
        
        // 如果文件已经很小，直接返回
        if (originalSize <= targetSize) {
          resolve(imagePath);
          return;
        }
        
        // 计算压缩比例
        const ratio = Math.sqrt(targetSize / originalSize);
        const quality = Math.max(0.3, Math.min(0.9, ratio));
        
        // 获取图片信息
        wx.getImageInfo({
          src: imagePath,
          success: async (imageInfo) => {
            try {
              const { width, height } = imageInfo;
              const newWidth = Math.floor(width * ratio);
              const newHeight = Math.floor(height * ratio);
              
              const compressedPath = await this.compressWithCanvas(
                imagePath, 
                newWidth, 
                newHeight, 
                quality
              );
              
              // 检查压缩后的大小
              const compressedSize = await this.getImageSize(compressedPath);
              
              // 如果还是太大，进一步压缩
              if (compressedSize > targetSize && quality > 0.3) {
                const newQuality = Math.max(0.3, quality * 0.8);
                const finalPath = await this.compressWithCanvas(
                  compressedPath,
                  newWidth,
                  newHeight,
                  newQuality
                );
                resolve(finalPath);
              } else {
                resolve(compressedPath);
              }
            } catch (error) {
              reject(error);
            }
          },
          fail: reject
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 选择并压缩图片
   * @param {Object} options 选择选项
   * @returns {Promise<string>} 压缩后的图片路径
   */
  chooseAndCompressImage(options = {}) {
    return new Promise((resolve, reject) => {
      const defaultOptions = {
        count: 1,
        sizeType: ['original'],
        sourceType: ['album', 'camera'],
        ...options
      };
      
      wx.chooseImage({
        ...defaultOptions,
        success: async (res) => {
          try {
            const imagePath = res.tempFilePaths[0];
            const compressedPath = await this.smartCompress(imagePath);
            resolve(compressedPath);
          } catch (error) {
            reject(error);
          }
        },
        fail: reject
      });
    });
  }

  /**
   * 图片格式转换
   * @param {string} imagePath 图片路径
   * @param {string} format 目标格式 ('jpg' | 'png')
   * @returns {Promise<string>} 转换后的图片路径
   */
  convertFormat(imagePath, format = 'jpg') {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: imagePath,
        success: (imageInfo) => {
          const { width, height } = imageInfo;
          
          const canvas = wx.createOffscreenCanvas({ type: '2d', width, height });
          const ctx = canvas.getContext('2d');
          const img = canvas.createImage();
          
          img.onload = () => {
            ctx.drawImage(img, 0, 0, width, height);
            
            wx.canvasToTempFilePath({
              canvas,
              fileType: format,
              quality: 0.9,
              success: (res) => {
                resolve(res.tempFilePath);
              },
              fail: reject
            });
          };
          
          img.onerror = reject;
          img.src = imagePath;
        },
        fail: reject
      });
    });
  }

  /**
   * 生成图片缩略图
   * @param {string} imagePath 图片路径
   * @param {number} size 缩略图尺寸
   * @returns {Promise<string>} 缩略图路径
   */
  generateThumbnail(imagePath, size = 200) {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: imagePath,
        success: (imageInfo) => {
          const { width, height } = imageInfo;
          
          // 计算缩略图尺寸（保持比例）
          let thumbWidth, thumbHeight;
          if (width > height) {
            thumbWidth = size;
            thumbHeight = Math.floor((height * size) / width);
          } else {
            thumbHeight = size;
            thumbWidth = Math.floor((width * size) / height);
          }
          
          this.compressWithCanvas(imagePath, thumbWidth, thumbHeight, 0.8)
            .then(resolve)
            .catch(reject);
        },
        fail: reject
      });
    });
  }

  /**
   * 批量压缩图片
   * @param {Array<string>} imagePaths 图片路径数组
   * @param {Object} options 压缩选项
   * @returns {Promise<Array<string>>} 压缩后的图片路径数组
   */
  batchCompress(imagePaths, options = {}) {
    const promises = imagePaths.map(path => this.smartCompress(path, options.targetSize));
    return Promise.all(promises);
  }
}

// 创建实例
const imageUtils = new ImageUtils();

// 导出常用方法
export const compressImage = imageUtils.compressImage.bind(imageUtils);
export const smartCompress = imageUtils.smartCompress.bind(imageUtils);
export const chooseAndCompressImage = imageUtils.chooseAndCompressImage.bind(imageUtils);
export const getImageSize = imageUtils.getImageSize.bind(imageUtils);
export const convertFormat = imageUtils.convertFormat.bind(imageUtils);
export const generateThumbnail = imageUtils.generateThumbnail.bind(imageUtils);
export const batchCompress = imageUtils.batchCompress.bind(imageUtils);

export default imageUtils;
