<!--关于页面-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <van-icon name="arrow-left" size="20" color="#333" />
      </view>
      <view class="navbar-title">关于我们</view>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 应用信息 -->
  <view class="app-info">
    <image class="app-logo" src="/images/logo.png" mode="aspectFit" />
    <text class="app-name">AI智能菜谱</text>
    <text class="app-slogan">让AI为你定制专属菜谱</text>
    <text class="app-version">版本 {{appVersion}}</text>
  </view>

  <!-- 功能介绍 -->
  <view class="feature-section">
    <text class="section-title">核心功能</text>
    <view class="feature-list">
      <view class="feature-item">
        <view class="feature-icon">
          <van-icon name="photo-o" size="24" color="#4CAF50" />
        </view>
        <view class="feature-content">
          <text class="feature-name">智能识别</text>
          <text class="feature-desc">拍照识别食材，准确率高达95%</text>
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">
          <van-icon name="star-o" size="24" color="#FF9800" />
        </view>
        <view class="feature-content">
          <text class="feature-name">AI生成菜谱</text>
          <text class="feature-desc">根据食材智能推荐个性化菜谱</text>
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">
          <van-icon name="like-o" size="24" color="#E91E63" />
        </view>
        <view class="feature-content">
          <text class="feature-name">个性化推荐</text>
          <text class="feature-desc">基于口味偏好的精准推荐</text>
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">
          <van-icon name="friends-o" size="24" color="#2196F3" />
        </view>
        <view class="feature-content">
          <text class="feature-name">社区分享</text>
          <text class="feature-desc">与美食爱好者分享烹饪心得</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据统计 -->
  <view class="stats-section">
    <text class="section-title">使用统计</text>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{stats.totalUsers}}</text>
        <text class="stat-label">用户数量</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.totalRecipes}}</text>
        <text class="stat-label">生成菜谱</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.totalRecognitions}}</text>
        <text class="stat-label">识别次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.accuracy}}</text>
        <text class="stat-label">识别准确率</text>
      </view>
    </view>
  </view>

  <!-- 团队介绍 -->
  <view class="team-section">
    <text class="section-title">开发团队</text>
    <view class="team-intro">
      <text class="team-desc">
        我们是一支专注于AI技术与美食结合的创新团队，致力于用科技让烹饪变得更简单、更有趣。
        团队成员来自知名互联网公司和AI研究机构，在人工智能、图像识别、自然语言处理等领域拥有丰富经验。
      </text>
    </view>
    
    <view class="team-members">
      <view class="member-item" wx:for="{{teamMembers}}" wx:key="id">
        <image class="member-avatar" src="{{item.avatar}}" mode="aspectFill" />
        <view class="member-info">
          <text class="member-name">{{item.name}}</text>
          <text class="member-role">{{item.role}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 技术栈 -->
  <view class="tech-section">
    <text class="section-title">技术架构</text>
    <view class="tech-stack">
      <view class="tech-category">
        <text class="tech-title">前端技术</text>
        <view class="tech-tags">
          <text class="tech-tag" wx:for="{{techStack.frontend}}" wx:key="*this">{{item}}</text>
        </view>
      </view>
      
      <view class="tech-category">
        <text class="tech-title">后端技术</text>
        <view class="tech-tags">
          <text class="tech-tag" wx:for="{{techStack.backend}}" wx:key="*this">{{item}}</text>
        </view>
      </view>
      
      <view class="tech-category">
        <text class="tech-title">AI技术</text>
        <view class="tech-tags">
          <text class="tech-tag" wx:for="{{techStack.ai}}" wx:key="*this">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-section">
    <text class="section-title">联系我们</text>
    <view class="contact-list">
      <view class="contact-item" bindtap="copyContact" data-type="email">
        <view class="contact-icon">
          <van-icon name="envelop-o" size="20" color="#4CAF50" />
        </view>
        <view class="contact-content">
          <text class="contact-label">邮箱</text>
          <text class="contact-value"><EMAIL></text>
        </view>
        <van-icon name="copy" size="16" color="#999" />
      </view>
      
      <view class="contact-item" bindtap="copyContact" data-type="wechat">
        <view class="contact-icon">
          <van-icon name="wechat" size="20" color="#07C160" />
        </view>
        <view class="contact-content">
          <text class="contact-label">微信</text>
          <text class="contact-value">airecipe_service</text>
        </view>
        <van-icon name="copy" size="16" color="#999" />
      </view>
      
      <view class="contact-item" bindtap="copyContact" data-type="qq">
        <view class="contact-icon">
          <van-icon name="qq" size="20" color="#1296DB" />
        </view>
        <view class="contact-content">
          <text class="contact-label">QQ群</text>
          <text class="contact-value">123456789</text>
        </view>
        <van-icon name="copy" size="16" color="#999" />
      </view>
    </view>
  </view>

  <!-- 更新日志 -->
  <view class="changelog-section">
    <text class="section-title">更新日志</text>
    <view class="changelog-list">
      <view class="changelog-item" wx:for="{{changelog}}" wx:key="version">
        <view class="changelog-header">
          <text class="changelog-version">v{{item.version}}</text>
          <text class="changelog-date">{{item.date}}</text>
        </view>
        <view class="changelog-content">
          <text class="changelog-feature" wx:for="{{item.features}}" wx:key="*this" wx:for-item="feature">
            • {{feature}}
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 法律信息 -->
  <view class="legal-section">
    <text class="section-title">法律信息</text>
    <view class="legal-links">
      <text class="legal-link" bindtap="openPrivacyPolicy">隐私政策</text>
      <text class="legal-link" bindtap="openUserAgreement">用户协议</text>
      <text class="legal-link" bindtap="openCopyright">版权声明</text>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer-info">
    <text class="copyright">© 2024 AI智能菜谱. All rights reserved.</text>
    <text class="company">由 AI Recipe Team 开发</text>
    <text class="icp">ICP备案号：京ICP备2024000000号</text>
  </view>
</view>

<!-- 全局提示 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
