/* 关于页面样式 */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 60rpx;
  height: 60rpx;
}

/* 应用信息 */
.app-info {
  margin-top: calc(88rpx + var(--status-bar-height, 44rpx) + 40rpx);
  background: white;
  padding: 60rpx 40rpx;
  text-align: center;
  border-radius: 20rpx;
  margin: calc(88rpx + var(--status-bar-height, 44rpx) + 40rpx) 32rpx 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  background: #f0f0f0;
}

.app-name {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.app-slogan {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.app-version {
  display: block;
  font-size: 24rpx;
  color: #999;
  background: #f8f8f8;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 通用区块样式 */
.feature-section,
.stats-section,
.team-section,
.tech-section,
.contact-section,
.changelog-section,
.legal-section {
  background: white;
  margin: 0 32rpx 40rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 功能介绍 */
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.feature-icon {
  width: 60rpx;
  height: 60rpx;
  background: #f8f8f8;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.feature-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 数据统计 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.stat-item {
  text-align: center;
  padding: 30rpx 20rpx;
  background: #f8f8f8;
  border-radius: 16rpx;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #4CAF50;
  margin-bottom: 12rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 团队介绍 */
.team-intro {
  margin-bottom: 40rpx;
}

.team-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.team-members {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 16rpx;
}

.member-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #e0e0e0;
}

.member-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.member-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.member-role {
  font-size: 24rpx;
  color: #666;
}

/* 技术栈 */
.tech-stack {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.tech-category {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tech-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tech-tag {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid #e0e0e0;
}

/* 联系方式 */
.contact-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx;
  background: #f8f8f8;
  border-radius: 16rpx;
  transition: background 0.3s ease;
}

.contact-item:active {
  background: #f0f0f0;
}

.contact-icon {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  border-radius: 50%;
}

.contact-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.contact-label {
  font-size: 24rpx;
  color: #999;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 更新日志 */
.changelog-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.changelog-item {
  border-left: 4rpx solid #4CAF50;
  padding-left: 20rpx;
}

.changelog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.changelog-version {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.changelog-date {
  font-size: 24rpx;
  color: #999;
}

.changelog-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.changelog-feature {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 法律信息 */
.legal-links {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.legal-link {
  font-size: 28rpx;
  color: #4CAF50;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  background: #f0f8f0;
  transition: all 0.3s ease;
}

.legal-link:active {
  background: #e8f5e8;
  transform: scale(0.95);
}

/* 底部信息 */
.footer-info {
  text-align: center;
  padding: 40rpx;
  margin: 0 32rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.copyright,
.company,
.icp {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.8;
}
