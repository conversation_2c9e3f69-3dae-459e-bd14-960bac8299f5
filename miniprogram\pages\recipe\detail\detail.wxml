<!--菜谱详情页面-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <van-icon name="arrow-left" size="20" color="white" />
      </view>
      <view class="navbar-title">{{recipe.name || '菜谱详情'}}</view>
      <view class="navbar-right">
        <view class="nav-action" bindtap="toggleFavorite">
          <van-icon 
            name="{{recipe.isFavorite ? 'star' : 'star-o'}}" 
            size="20" 
            color="{{recipe.isFavorite ? '#FFD700' : 'white'}}" 
          />
        </view>
        <view class="nav-action" bindtap="shareRecipe">
          <van-icon name="share-o" size="20" color="white" />
        </view>
      </view>
    </view>
  </view>

  <!-- 菜谱头图 -->
  <view class="recipe-header">
    <image 
      class="header-image" 
      src="{{recipe.image || '/images/default-recipe.png'}}" 
      mode="aspectFill"
      bindtap="previewImage"
    />
    <view class="header-overlay">
      <view class="recipe-basic-info">
        <text class="recipe-name">{{recipe.name}}</text>
        <text class="recipe-description" wx:if="{{recipe.description}}">{{recipe.description}}</text>
        
        <view class="recipe-meta">
          <view class="meta-item">
            <van-icon name="clock-o" size="14" color="white" />
            <text>{{recipe.cookTimeText}}</text>
          </view>
          <view class="meta-item">
            <van-icon name="fire-o" size="14" color="white" />
            <text>{{recipe.difficultyText}}</text>
          </view>
          <view class="meta-item">
            <van-icon name="friends-o" size="14" color="white" />
            <text>{{recipe.servings || 2}}人份</text>
          </view>
          <view class="meta-item" wx:if="{{recipe.rating}}">
            <van-icon name="star" size="14" color="#FFD700" />
            <text>{{recipe.rating}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-section">
    <!-- 营养信息 -->
    <view class="nutrition-section" wx:if="{{recipe.nutrition}}">
      <view class="section-header">
        <van-icon name="fire-o" size="18" color="#FF5722" />
        <text class="section-title">营养信息</text>
        <text class="per-serving">每份</text>
      </view>
      
      <view class="nutrition-grid">
        <view class="nutrition-item" wx:for="{{nutritionInfo}}" wx:key="key">
          <view class="nutrition-icon">{{item.icon}}</view>
          <view class="nutrition-info">
            <text class="nutrition-name">{{item.name}}</text>
            <text class="nutrition-value">{{item.formatted}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 食材清单 -->
    <view class="ingredients-section" wx:if="{{recipe.ingredients && recipe.ingredients.length > 0}}">
      <view class="section-header">
        <van-icon name="apps-o" size="18" color="#4CAF50" />
        <text class="section-title">食材清单</text>
        <text class="ingredient-count">({{recipe.ingredients.length}}种)</text>
      </view>
      
      <view class="ingredients-list">
        <view 
          class="ingredient-item" 
          wx:for="{{recipe.ingredients}}" 
          wx:key="name"
        >
          <view class="ingredient-info">
            <text class="ingredient-name">{{item.name}}</text>
            <text class="ingredient-note" wx:if="{{item.note}}">{{item.note}}</text>
          </view>
          <view class="ingredient-quantity">
            <text>{{item.quantity}}{{item.unit}}</text>
          </view>
        </view>
      </view>
      
      <view class="ingredients-actions">
        <van-button 
          type="default" 
          size="small" 
          icon="shopping-cart-o"
          bindtap="addToShoppingList"
        >
          添加到购物清单
        </van-button>
      </view>
    </view>

    <!-- 制作步骤 -->
    <view class="steps-section" wx:if="{{recipe.steps && recipe.steps.length > 0}}">
      <view class="section-header">
        <van-icon name="orders-o" size="18" color="#2196F3" />
        <text class="section-title">制作步骤</text>
        <text class="step-count">({{recipe.steps.length}}步)</text>
      </view>
      
      <view class="steps-list">
        <view 
          class="step-item" 
          wx:for="{{recipe.steps}}" 
          wx:key="step"
        >
          <view class="step-number">{{index + 1}}</view>
          <view class="step-content">
            <view class="step-text">{{item.description}}</view>
            <image 
              class="step-image" 
              src="{{item.image}}" 
              mode="aspectFill"
              wx:if="{{item.image}}"
              bindtap="previewStepImage"
              data-url="{{item.image}}"
            />
            <view class="step-tips" wx:if="{{item.tips}}">
              <van-icon name="info-o" size="12" color="#FF9800" />
              <text>{{item.tips}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 烹饪技巧 -->
    <view class="tips-section" wx:if="{{recipe.cookingTips && recipe.cookingTips.length > 0}}">
      <view class="section-header">
        <van-icon name="bulb-o" size="18" color="#FF9800" />
        <text class="section-title">烹饪技巧</text>
      </view>
      
      <view class="tips-list">
        <view 
          class="tip-item" 
          wx:for="{{recipe.cookingTips}}" 
          wx:key="*this"
        >
          <van-icon name="arrow" size="12" color="#FF9800" />
          <text>{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 相关菜谱推荐 -->
    <view class="related-section" wx:if="{{relatedRecipes && relatedRecipes.length > 0}}">
      <view class="section-header">
        <van-icon name="bookmark-o" size="18" color="#9C27B0" />
        <text class="section-title">相关推荐</text>
      </view>
      
      <scroll-view class="related-scroll" scroll-x>
        <view class="related-list">
          <view 
            class="related-item" 
            wx:for="{{relatedRecipes}}" 
            wx:key="id"
            bindtap="viewRelatedRecipe"
            data-recipe="{{item}}"
          >
            <image class="related-image" src="{{item.image || '/images/default-recipe.png'}}" mode="aspectFill" />
            <view class="related-info">
              <text class="related-name">{{item.name}}</text>
              <text class="related-time">{{item.cookTimeText}}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <van-button 
      type="default" 
      size="large" 
      icon="clock-o"
      bindtap="startCooking"
    >
      开始制作
    </van-button>
    <van-button 
      type="primary" 
      size="large" 
      icon="chat-o"
      bindtap="showFeedback"
    >
      评价反馈
    </van-button>
  </view>
</view>

<!-- 制作模式弹窗 -->
<van-popup 
  show="{{showCookingModal}}" 
  position="bottom" 
  round
  bind:close="hideCookingModal"
>
  <view class="cooking-modal">
    <view class="modal-header">
      <text class="modal-title">制作模式</text>
      <van-icon name="cross" size="18" bindtap="hideCookingModal" />
    </view>
    
    <view class="cooking-options">
      <view class="option-item" bindtap="startStepByStep">
        <van-icon name="orders-o" size="24" color="#4CAF50" />
        <view class="option-info">
          <text class="option-name">分步制作</text>
          <text class="option-desc">逐步指导，不会遗漏</text>
        </view>
        <van-icon name="arrow" size="16" color="#999" />
      </view>
      
      <view class="option-item" bindtap="startTimer">
        <van-icon name="clock-o" size="24" color="#2196F3" />
        <view class="option-info">
          <text class="option-name">定时提醒</text>
          <text class="option-desc">设置烹饪时间提醒</text>
        </view>
        <van-icon name="arrow" size="16" color="#999" />
      </view>
    </view>
  </view>
</van-popup>

<!-- 全局提示 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<van-notify id="van-notify" />
