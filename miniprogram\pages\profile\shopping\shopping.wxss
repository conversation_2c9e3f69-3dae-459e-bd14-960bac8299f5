/* 购物清单页面样式 */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.edit-btn {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 500;
}

/* 统计信息栏 */
.stats-bar {
  margin-top: calc(88rpx + var(--status-bar-height, 44rpx) + 20rpx);
  padding: 24rpx 32rpx;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.stats-left {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.stats-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.checked-text {
  font-size: 24rpx;
  color: #999;
}

.stats-actions {
  display: flex;
  gap: 32rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: #f8f8f8;
  transition: all 0.3s ease;
}

.action-btn:active {
  background: #e8e8e8;
  transform: scale(0.95);
}

/* 快速添加栏 */
.quick-add {
  background: white;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #eee;
}

/* 购物清单列表 */
.shopping-list {
  padding: 20rpx 32rpx;
}

.category-group {
  margin-bottom: 40rpx;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  margin-bottom: 16rpx;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.category-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.item-count {
  font-size: 24rpx;
  color: #999;
}

.category-actions {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 26rpx;
  color: #4CAF50;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: #f0f8f0;
}

.shopping-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
}

.shopping-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.shopping-item.checked {
  opacity: 0.6;
  background: #f8f8f8;
}

.shopping-item.edit-mode {
  padding-right: 120rpx;
}

/* 选择框 */
.item-checkbox {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 食材信息 */
.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.item-name.checked-text {
  text-decoration: line-through;
  color: #999;
}

.item-meta {
  display: flex;
  gap: 16rpx;
}

.meta-item {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.add-time {
  font-size: 22rpx;
  color: #ccc;
}

/* 操作按钮 */
.item-actions {
  position: absolute;
  right: 60rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 16rpx;
}

.item-actions .action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f8f8f8;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  gap: 0;
}

.item-actions .action-btn:active {
  background: #e8e8e8;
}

/* 拖拽手柄 */
.drag-handle {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 50;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 28rpx;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

/* 编辑弹窗 */
.edit-modal {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 0;
  max-height: 80vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.edit-form {
  padding: 30rpx 0;
  max-height: 50vh;
  overflow-y: auto;
}

.edit-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx 40rpx 0;
  border-top: 1rpx solid #eee;
}

/* 分类选择器 */
.category-picker {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 0;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.category-options {
  padding: 20rpx 0;
  max-height: 60vh;
  overflow-y: auto;
}

.category-option {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx 40rpx;
  transition: background 0.3s ease;
}

.category-option:active {
  background: #f8f8f8;
}

.category-option.active {
  background: #f0f8f0;
}

.option-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.category-option.active .option-text {
  color: #4CAF50;
  font-weight: 500;
}

/* 菜谱选择弹窗 */
.recipe-modal {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 0;
  max-height: 70vh;
}

.recipe-list {
  padding: 20rpx 0;
  max-height: 50vh;
  overflow-y: auto;
}

.recipe-item {
  display: flex;
  align-items: center;
  padding: 24rpx 40rpx;
  transition: background 0.3s ease;
}

.recipe-item:active {
  background: #f8f8f8;
}

.recipe-thumb {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background: #f0f0f0;
  margin-right: 20rpx;
}

.recipe-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.recipe-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.ingredient-count {
  font-size: 24rpx;
  color: #999;
}
