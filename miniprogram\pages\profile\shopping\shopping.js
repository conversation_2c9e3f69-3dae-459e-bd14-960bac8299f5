// 购物清单页面逻辑
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import Notify from '@vant/weapp/notify/notify';
import { get, post, put, del } from '../../../utils/api';
import { getStorage, setStorage, CACHE_KEYS } from '../../../utils/storage';
import { formatTime } from '../../../utils/format';

Page({
  data: {
    // 购物清单数据
    shoppingList: [],
    displayList: [],
    groupedList: [],
    
    // 统计数据
    checkedCount: 0,
    uncheckedCount: 0,
    selectedCount: 0,
    allSelected: false,
    
    // UI状态
    loading: false,
    editMode: false,
    showEditModal: false,
    showCategoryPicker: false,
    showRecipeModal: false,
    
    // 快速添加
    newItemName: '',
    
    // 编辑表单
    editingItem: null,
    editForm: {
      name: '',
      quantity: '',
      unit: '',
      category: 'other',
      categoryText: '其他',
      note: ''
    },
    
    // 分类选项
    categoryOptions: [
      { label: '蔬菜', value: 'vegetable', icon: 'leaf', color: '#4CAF50' },
      { label: '肉类', value: 'meat', icon: 'fire', color: '#FF5722' },
      { label: '海鲜', value: 'seafood', icon: 'gem', color: '#2196F3' },
      { label: '水果', value: 'fruit', icon: 'smile', color: '#FF9800' },
      { label: '调料', value: 'seasoning', icon: 'star', color: '#9C27B0' },
      { label: '主食', value: 'staple', icon: 'bag', color: '#795548' },
      { label: '其他', value: 'other', icon: 'more', color: '#607D8B' }
    ],
    
    // 最近菜谱
    recentRecipes: []
  },

  onLoad() {
    console.log('Shopping page loaded');
    this.loadShoppingList();
    this.loadRecentRecipes();
  },

  onShow() {
    // 每次显示时刷新数据
    this.loadShoppingList();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 加载购物清单
  async loadShoppingList() {
    try {
      this.setData({ loading: true });
      
      // 先从本地缓存获取
      const cachedList = getStorage(CACHE_KEYS.SHOPPING_LIST, []);
      if (cachedList.length > 0) {
        this.processShoppingList(cachedList);
      }
      
      // 从服务器获取最新数据
      const response = await get('/api/users/shopping-list');
      if (response.success) {
        const shoppingList = response.data.map(item => ({
          ...item,
          selected: false,
          addTimeText: formatTime(item.createdAt, 'MM-DD HH:mm')
        }));
        
        // 缓存到本地
        setStorage(CACHE_KEYS.SHOPPING_LIST, shoppingList);
        this.processShoppingList(shoppingList);
      }
    } catch (error) {
      console.error('Load shopping list failed:', error);
      Toast.fail('加载失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 处理购物清单数据
  processShoppingList(shoppingList) {
    const displayList = [...shoppingList];
    const groupedList = this.groupByCategory(displayList);
    const checkedCount = shoppingList.filter(item => item.checked).length;
    const uncheckedCount = shoppingList.length - checkedCount;
    
    this.setData({ 
      shoppingList,
      displayList,
      groupedList,
      checkedCount,
      uncheckedCount
    });
  },

  // 按分类分组
  groupByCategory(list) {
    const groups = {};
    
    list.forEach(item => {
      const category = item.category || 'other';
      if (!groups[category]) {
        const categoryInfo = this.data.categoryOptions.find(opt => opt.value === category) || 
                           this.data.categoryOptions.find(opt => opt.value === 'other');
        
        groups[category] = {
          category,
          categoryName: categoryInfo.label,
          icon: categoryInfo.icon,
          color: categoryInfo.color,
          items: [],
          allSelected: false
        };
      }
      
      groups[category].items.push(item);
    });
    
    // 检查每个分类是否全选
    Object.values(groups).forEach(group => {
      group.allSelected = group.items.length > 0 && 
                         group.items.every(item => item.selected);
    });
    
    return Object.values(groups).sort((a, b) => a.categoryName.localeCompare(b.categoryName));
  },

  // 切换编辑模式
  toggleEditMode() {
    const editMode = !this.data.editMode;
    this.setData({ 
      editMode,
      selectedCount: 0,
      allSelected: false
    });
    
    // 重置选择状态
    if (!editMode) {
      const shoppingList = this.data.shoppingList.map(item => ({
        ...item,
        selected: false
      }));
      this.processShoppingList(shoppingList);
    }
  },

  // 快速添加输入变化
  onNewItemChange(e) {
    this.setData({ newItemName: e.detail });
  },

  // 快速添加新项目
  async addNewItem() {
    const name = this.data.newItemName.trim();
    if (!name) return;
    
    try {
      const newItem = {
        name,
        quantity: '',
        unit: '',
        category: 'other',
        note: '',
        checked: false
      };
      
      const response = await post('/api/users/shopping-list', newItem);
      if (response.success) {
        this.setData({ newItemName: '' });
        this.loadShoppingList();
        Toast.success('添加成功');
      }
    } catch (error) {
      console.error('Add item failed:', error);
      Toast.fail('添加失败，请重试');
    }
  },

  // 切换选中状态
  async toggleCheck(e) {
    const id = e.currentTarget.dataset.id;
    
    try {
      const response = await put(`/api/users/shopping-list/${id}/toggle`);
      if (response.success) {
        const shoppingList = this.data.shoppingList.map(item => {
          if (item.id === id) {
            return { ...item, checked: !item.checked };
          }
          return item;
        });
        
        setStorage(CACHE_KEYS.SHOPPING_LIST, shoppingList);
        this.processShoppingList(shoppingList);
      }
    } catch (error) {
      console.error('Toggle check failed:', error);
      Toast.fail('操作失败，请重试');
    }
  },

  // 显示添加/编辑弹窗
  addItem() {
    this.setData({
      showEditModal: true,
      editingItem: null,
      editForm: {
        name: '',
        quantity: '',
        unit: '',
        category: 'other',
        categoryText: '其他',
        note: ''
      }
    });
  },

  // 编辑项目
  editItem(e) {
    e.stopPropagation();
    const item = e.currentTarget.dataset.item;
    const categoryInfo = this.data.categoryOptions.find(opt => opt.value === item.category) || 
                        this.data.categoryOptions.find(opt => opt.value === 'other');
    
    this.setData({
      showEditModal: true,
      editingItem: item,
      editForm: {
        name: item.name,
        quantity: item.quantity || '',
        unit: item.unit || '',
        category: item.category || 'other',
        categoryText: categoryInfo.label,
        note: item.note || ''
      }
    });
  },

  // 隐藏编辑弹窗
  hideEditModal() {
    this.setData({ showEditModal: false });
  },

  // 表单输入变化
  onFormChange(e) {
    const field = e.currentTarget.dataset.field;
    this.setData({
      [`editForm.${field}`]: e.detail
    });
  },

  // 显示分类选择器
  showCategoryPicker() {
    this.setData({ showCategoryPicker: true });
  },

  // 隐藏分类选择器
  hideCategoryPicker() {
    this.setData({ showCategoryPicker: false });
  },

  // 选择分类
  selectCategory(e) {
    const category = e.currentTarget.dataset.category;
    const categoryInfo = this.data.categoryOptions.find(opt => opt.value === category);
    
    this.setData({
      'editForm.category': category,
      'editForm.categoryText': categoryInfo.label,
      showCategoryPicker: false
    });
  },

  // 保存项目
  async saveItem() {
    const { editForm, editingItem } = this.data;
    
    if (!editForm.name.trim()) {
      Toast.fail('请输入食材名称');
      return;
    }
    
    try {
      const itemData = {
        name: editForm.name.trim(),
        quantity: editForm.quantity,
        unit: editForm.unit,
        category: editForm.category,
        note: editForm.note
      };
      
      let response;
      if (editingItem) {
        response = await put(`/api/users/shopping-list/${editingItem.id}`, itemData);
      } else {
        response = await post('/api/users/shopping-list', { ...itemData, checked: false });
      }
      
      if (response.success) {
        this.setData({ showEditModal: false });
        this.loadShoppingList();
        Toast.success(editingItem ? '修改成功' : '添加成功');
      }
    } catch (error) {
      console.error('Save item failed:', error);
      Toast.fail('保存失败，请重试');
    }
  },

  // 删除项目
  async deleteItem(e) {
    e.stopPropagation();
    const id = e.currentTarget.dataset.id;
    
    try {
      const result = await Dialog.confirm({
        title: '确认删除',
        message: '确定要删除这个食材吗？'
      });
      
      if (result) {
        const response = await del(`/api/users/shopping-list/${id}`);
        if (response.success) {
          this.loadShoppingList();
          Toast.success('删除成功');
        }
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('Delete item failed:', error);
        Toast.fail('删除失败，请重试');
      }
    }
  },

  // 清理已购买项目
  async clearChecked() {
    const checkedItems = this.data.shoppingList.filter(item => item.checked);
    if (checkedItems.length === 0) {
      Toast('没有已购买的项目');
      return;
    }
    
    try {
      const result = await Dialog.confirm({
        title: '确认清理',
        message: `确定要清理 ${checkedItems.length} 个已购买的项目吗？`
      });
      
      if (result) {
        const ids = checkedItems.map(item => item.id);
        const response = await post('/api/users/shopping-list/batch-delete', { ids });
        if (response.success) {
          this.loadShoppingList();
          Toast.success('清理完成');
        }
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('Clear checked failed:', error);
        Toast.fail('清理失败，请重试');
      }
    }
  },

  // 全选/取消全选
  toggleSelectAll() {
    const allSelected = !this.data.allSelected;
    const shoppingList = this.data.shoppingList.map(item => ({
      ...item,
      selected: allSelected
    }));
    
    const selectedCount = allSelected ? shoppingList.length : 0;
    
    this.setData({ 
      shoppingList,
      selectedCount,
      allSelected
    });
    this.processShoppingList(shoppingList);
  },

  // 批量删除
  async batchDelete() {
    if (this.data.selectedCount === 0) return;
    
    try {
      const result = await Dialog.confirm({
        title: '确认删除',
        message: `确定要删除选中的 ${this.data.selectedCount} 个项目吗？`
      });
      
      if (result) {
        const selectedIds = this.data.shoppingList
          .filter(item => item.selected)
          .map(item => item.id);
        
        const response = await post('/api/users/shopping-list/batch-delete', { ids: selectedIds });
        if (response.success) {
          this.setData({ 
            editMode: false,
            selectedCount: 0,
            allSelected: false
          });
          this.loadShoppingList();
          Toast.success(`已删除 ${selectedIds.length} 个项目`);
        }
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('Batch delete failed:', error);
        Toast.fail('删除失败，请重试');
      }
    }
  },

  // 分享清单
  shareList() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    Toast.success('分享功能开发中');
  },

  // 去购买
  goShopping() {
    Toast.success('购买功能开发中');
  },

  // 加载最近菜谱
  async loadRecentRecipes() {
    try {
      const response = await get('/api/users/recent-recipes');
      if (response.success) {
        this.setData({ recentRecipes: response.data.slice(0, 5) });
      }
    } catch (error) {
      console.error('Load recent recipes failed:', error);
    }
  },

  // 显示从菜谱添加弹窗
  addFromRecipe() {
    this.setData({ showRecipeModal: true });
  },

  // 隐藏菜谱弹窗
  hideRecipeModal() {
    this.setData({ showRecipeModal: false });
  },

  // 从菜谱添加食材
  async addFromRecipeItem(e) {
    const recipe = e.currentTarget.dataset.recipe;
    
    try {
      const response = await post('/api/users/shopping-list/from-recipe', {
        recipeId: recipe.id
      });
      
      if (response.success) {
        this.setData({ showRecipeModal: false });
        this.loadShoppingList();
        Toast.success(`已添加 ${response.data.addedCount} 种食材`);
      }
    } catch (error) {
      console.error('Add from recipe failed:', error);
      Toast.fail('添加失败，请重试');
    }
  }
});
