// 格式化工具类

/**
 * 格式化工具类
 */
class FormatUtils {
  
  /**
   * 格式化时间
   * @param {Date|string|number} date 时间
   * @param {string} format 格式 (YYYY-MM-DD HH:mm:ss)
   * @returns {string} 格式化后的时间
   */
  formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hour = String(d.getHours()).padStart(2, '0');
    const minute = String(d.getMinutes()).padStart(2, '0');
    const second = String(d.getSeconds()).padStart(2, '0');
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second);
  }

  /**
   * 格式化相对时间
   * @param {Date|string|number} date 时间
   * @returns {string} 相对时间描述
   */
  formatRelativeTime(date) {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    const now = new Date();
    const diff = now.getTime() - d.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);
    const years = Math.floor(months / 12);
    
    if (seconds < 60) {
      return '刚刚';
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 30) {
      return `${days}天前`;
    } else if (months < 12) {
      return `${months}个月前`;
    } else {
      return `${years}年前`;
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @param {number} decimals 小数位数
   * @returns {string} 格式化后的文件大小
   */
  formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  /**
   * 格式化数字
   * @param {number} num 数字
   * @param {number} decimals 小数位数
   * @returns {string} 格式化后的数字
   */
  formatNumber(num, decimals = 0) {
    if (isNaN(num)) return '0';
    
    return Number(num).toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  }

  /**
   * 格式化价格
   * @param {number} price 价格
   * @param {string} currency 货币符号
   * @returns {string} 格式化后的价格
   */
  formatPrice(price, currency = '¥') {
    if (isNaN(price)) return `${currency}0.00`;
    
    return `${currency}${Number(price).toFixed(2)}`;
  }

  /**
   * 格式化百分比
   * @param {number} value 数值
   * @param {number} total 总数
   * @param {number} decimals 小数位数
   * @returns {string} 百分比
   */
  formatPercentage(value, total, decimals = 1) {
    if (total === 0) return '0%';
    
    const percentage = (value / total) * 100;
    return `${percentage.toFixed(decimals)}%`;
  }

  /**
   * 格式化手机号
   * @param {string} phone 手机号
   * @returns {string} 格式化后的手机号
   */
  formatPhone(phone) {
    if (!phone) return '';
    
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 11) {
      return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
    }
    return phone;
  }

  /**
   * 隐藏手机号中间4位
   * @param {string} phone 手机号
   * @returns {string} 隐藏后的手机号
   */
  hidePhone(phone) {
    if (!phone) return '';
    
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 11) {
      return cleaned.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
    return phone;
  }

  /**
   * 格式化营养成分
   * @param {Object} nutrition 营养成分对象
   * @returns {Array} 格式化后的营养成分数组
   */
  formatNutrition(nutrition) {
    if (!nutrition) return [];
    
    const nutritionMap = {
      calories: { name: '热量', unit: 'kcal', icon: '🔥' },
      protein: { name: '蛋白质', unit: 'g', icon: '💪' },
      carbs: { name: '碳水化合物', unit: 'g', icon: '🌾' },
      fat: { name: '脂肪', unit: 'g', icon: '🥑' },
      fiber: { name: '膳食纤维', unit: 'g', icon: '🌿' },
      sugar: { name: '糖分', unit: 'g', icon: '🍯' },
      sodium: { name: '钠', unit: 'mg', icon: '🧂' },
      calcium: { name: '钙', unit: 'mg', icon: '🦴' },
      iron: { name: '铁', unit: 'mg', icon: '⚡' },
      vitaminC: { name: '维生素C', unit: 'mg', icon: '🍊' }
    };
    
    return Object.keys(nutrition)
      .filter(key => nutrition[key] > 0 && nutritionMap[key])
      .map(key => ({
        key,
        name: nutritionMap[key].name,
        value: nutrition[key],
        unit: nutritionMap[key].unit,
        icon: nutritionMap[key].icon,
        formatted: `${nutrition[key]}${nutritionMap[key].unit}`
      }));
  }

  /**
   * 格式化制作时间
   * @param {number} minutes 分钟数
   * @returns {string} 格式化后的时间
   */
  formatCookTime(minutes) {
    if (!minutes || minutes <= 0) return '未知';
    
    if (minutes < 60) {
      return `${minutes}分钟`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      
      if (remainingMinutes === 0) {
        return `${hours}小时`;
      } else {
        return `${hours}小时${remainingMinutes}分钟`;
      }
    }
  }

  /**
   * 格式化难度等级
   * @param {number|string} difficulty 难度等级
   * @returns {Object} 格式化后的难度信息
   */
  formatDifficulty(difficulty) {
    const difficultyMap = {
      1: { name: '简单', color: '#4CAF50', icon: '⭐' },
      2: { name: '中等', color: '#FF9800', icon: '⭐⭐' },
      3: { name: '困难', color: '#F44336', icon: '⭐⭐⭐' },
      easy: { name: '简单', color: '#4CAF50', icon: '⭐' },
      medium: { name: '中等', color: '#FF9800', icon: '⭐⭐' },
      hard: { name: '困难', color: '#F44336', icon: '⭐⭐⭐' }
    };
    
    return difficultyMap[difficulty] || { name: '未知', color: '#999', icon: '❓' };
  }

  /**
   * 格式化食材列表
   * @param {Array} ingredients 食材数组
   * @returns {string} 格式化后的食材字符串
   */
  formatIngredients(ingredients) {
    if (!Array.isArray(ingredients) || ingredients.length === 0) {
      return '暂无食材信息';
    }
    
    return ingredients
      .map(ingredient => {
        if (typeof ingredient === 'string') {
          return ingredient;
        } else if (ingredient.name) {
          const amount = ingredient.amount ? ` ${ingredient.amount}` : '';
          const unit = ingredient.unit ? ingredient.unit : '';
          return `${ingredient.name}${amount}${unit}`;
        }
        return '';
      })
      .filter(Boolean)
      .join('、');
  }

  /**
   * 格式化菜谱步骤
   * @param {Array} steps 步骤数组
   * @returns {Array} 格式化后的步骤数组
   */
  formatRecipeSteps(steps) {
    if (!Array.isArray(steps)) return [];
    
    return steps.map((step, index) => ({
      index: index + 1,
      content: typeof step === 'string' ? step : step.content || '',
      image: typeof step === 'object' ? step.image : null,
      time: typeof step === 'object' ? step.time : null,
      tip: typeof step === 'object' ? step.tip : null
    }));
  }

  /**
   * 截断文本
   * @param {string} text 文本
   * @param {number} maxLength 最大长度
   * @param {string} suffix 后缀
   * @returns {string} 截断后的文本
   */
  truncateText(text, maxLength = 50, suffix = '...') {
    if (!text || text.length <= maxLength) return text || '';
    
    return text.substring(0, maxLength) + suffix;
  }

  /**
   * 高亮搜索关键词
   * @param {string} text 文本
   * @param {string} keyword 关键词
   * @returns {string} 高亮后的HTML
   */
  highlightKeyword(text, keyword) {
    if (!text || !keyword) return text || '';
    
    const regex = new RegExp(`(${keyword})`, 'gi');
    return text.replace(regex, '<span class="highlight">$1</span>');
  }
}

// 创建实例
const format = new FormatUtils();

// 导出常用方法
export const formatDate = format.formatDate.bind(format);
export const formatRelativeTime = format.formatRelativeTime.bind(format);
export const formatFileSize = format.formatFileSize.bind(format);
export const formatNumber = format.formatNumber.bind(format);
export const formatPrice = format.formatPrice.bind(format);
export const formatPercentage = format.formatPercentage.bind(format);
export const formatPhone = format.formatPhone.bind(format);
export const hidePhone = format.hidePhone.bind(format);
export const formatNutrition = format.formatNutrition.bind(format);
export const formatCookTime = format.formatCookTime.bind(format);
export const formatDifficulty = format.formatDifficulty.bind(format);
export const formatIngredients = format.formatIngredients.bind(format);
export const formatRecipeSteps = format.formatRecipeSteps.bind(format);
export const truncateText = format.truncateText.bind(format);
export const highlightKeyword = format.highlightKeyword.bind(format);

export default format;
