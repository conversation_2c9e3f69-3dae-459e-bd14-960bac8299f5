<!--设置页面-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <van-icon name="arrow-left" size="20" color="#333" />
      </view>
      <view class="navbar-title">设置</view>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 设置列表 -->
  <view class="settings-list">
    <!-- 账户设置 -->
    <view class="setting-section">
      <text class="section-title">账户设置</text>
      
      <view class="setting-item" bindtap="editProfile">
        <view class="item-left">
          <van-icon name="user-o" size="20" color="#4CAF50" />
          <text class="item-title">个人信息</text>
        </view>
        <view class="item-right">
          <text class="item-desc">编辑头像、昵称等</text>
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>

      <view class="setting-item" bindtap="managePreferences">
        <view class="item-left">
          <van-icon name="like-o" size="20" color="#FF9800" />
          <text class="item-title">偏好设置</text>
        </view>
        <view class="item-right">
          <text class="item-desc">口味、难度偏好</text>
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>

      <view class="setting-item" bindtap="privacySettings">
        <view class="item-left">
          <van-icon name="shield-o" size="20" color="#2196F3" />
          <text class="item-title">隐私设置</text>
        </view>
        <view class="item-right">
          <text class="item-desc">数据使用权限</text>
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>
    </view>

    <!-- 应用设置 -->
    <view class="setting-section">
      <text class="section-title">应用设置</text>
      
      <view class="setting-item">
        <view class="item-left">
          <van-icon name="bell-o" size="20" color="#9C27B0" />
          <text class="item-title">消息通知</text>
        </view>
        <view class="item-right">
          <van-switch 
            checked="{{settings.notifications}}" 
            bind:change="toggleNotifications"
            size="24"
          />
        </view>
      </view>

      <view class="setting-item">
        <view class="item-left">
          <van-icon name="volume-o" size="20" color="#FF5722" />
          <text class="item-title">声音提示</text>
        </view>
        <view class="item-right">
          <van-switch 
            checked="{{settings.sound}}" 
            bind:change="toggleSound"
            size="24"
          />
        </view>
      </view>

      <view class="setting-item">
        <view class="item-left">
          <van-icon name="photo-o" size="20" color="#607D8B" />
          <text class="item-title">自动保存图片</text>
        </view>
        <view class="item-right">
          <van-switch 
            checked="{{settings.autoSaveImages}}" 
            bind:change="toggleAutoSaveImages"
            size="24"
          />
        </view>
      </view>

      <view class="setting-item" bindtap="cacheSettings">
        <view class="item-left">
          <van-icon name="delete-o" size="20" color="#795548" />
          <text class="item-title">缓存管理</text>
        </view>
        <view class="item-right">
          <text class="item-desc">{{cacheSize}}</text>
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>
    </view>

    <!-- 功能设置 -->
    <view class="setting-section">
      <text class="section-title">功能设置</text>
      
      <view class="setting-item" bindtap="aiSettings">
        <view class="item-left">
          <van-icon name="star-o" size="20" color="#FFD700" />
          <text class="item-title">AI设置</text>
        </view>
        <view class="item-right">
          <text class="item-desc">识别精度、生成偏好</text>
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>

      <view class="setting-item" bindtap="languageSettings">
        <view class="item-left">
          <van-icon name="globe-o" size="20" color="#00BCD4" />
          <text class="item-title">语言设置</text>
        </view>
        <view class="item-right">
          <text class="item-desc">{{currentLanguage}}</text>
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>

      <view class="setting-item" bindtap="themeSettings">
        <view class="item-left">
          <van-icon name="brush-o" size="20" color="#E91E63" />
          <text class="item-title">主题设置</text>
        </view>
        <view class="item-right">
          <text class="item-desc">{{currentTheme}}</text>
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>
    </view>

    <!-- 帮助与反馈 -->
    <view class="setting-section">
      <text class="section-title">帮助与反馈</text>
      
      <view class="setting-item" bindtap="helpCenter">
        <view class="item-left">
          <van-icon name="question-o" size="20" color="#4CAF50" />
          <text class="item-title">帮助中心</text>
        </view>
        <view class="item-right">
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>

      <view class="setting-item" bindtap="feedback">
        <view class="item-left">
          <van-icon name="chat-o" size="20" color="#FF9800" />
          <text class="item-title">意见反馈</text>
        </view>
        <view class="item-right">
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>

      <view class="setting-item" bindtap="contactUs">
        <view class="item-left">
          <van-icon name="service-o" size="20" color="#2196F3" />
          <text class="item-title">联系我们</text>
        </view>
        <view class="item-right">
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>

      <view class="setting-item" bindtap="checkUpdate">
        <view class="item-left">
          <van-icon name="upgrade" size="20" color="#9C27B0" />
          <text class="item-title">检查更新</text>
        </view>
        <view class="item-right">
          <text class="item-desc">{{appVersion}}</text>
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>
    </view>

    <!-- 关于应用 -->
    <view class="setting-section">
      <text class="section-title">关于应用</text>
      
      <view class="setting-item" bindtap="aboutApp">
        <view class="item-left">
          <van-icon name="info-o" size="20" color="#607D8B" />
          <text class="item-title">关于我们</text>
        </view>
        <view class="item-right">
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>

      <view class="setting-item" bindtap="privacyPolicy">
        <view class="item-left">
          <van-icon name="description" size="20" color="#795548" />
          <text class="item-title">隐私政策</text>
        </view>
        <view class="item-right">
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>

      <view class="setting-item" bindtap="userAgreement">
        <view class="item-left">
          <van-icon name="notes-o" size="20" color="#FF5722" />
          <text class="item-title">用户协议</text>
        </view>
        <view class="item-right">
          <van-icon name="arrow" size="16" color="#ccc" />
        </view>
      </view>
    </view>

    <!-- 账户操作 -->
    <view class="setting-section">
      <view class="setting-item danger-item" bindtap="logout">
        <view class="item-left">
          <van-icon name="sign-out" size="20" color="#ff4444" />
          <text class="item-title danger-text">退出登录</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 缓存管理弹窗 -->
<van-popup 
  show="{{showCacheModal}}" 
  position="bottom" 
  round
  bind:close="hideCacheModal"
>
  <view class="cache-modal">
    <view class="modal-header">
      <text class="modal-title">缓存管理</text>
      <van-icon name="cross" size="18" bindtap="hideCacheModal" />
    </view>
    
    <view class="cache-info">
      <view class="cache-item">
        <text class="cache-label">图片缓存</text>
        <text class="cache-size">{{imageCache}}</text>
      </view>
      <view class="cache-item">
        <text class="cache-label">数据缓存</text>
        <text class="cache-size">{{dataCache}}</text>
      </view>
      <view class="cache-item">
        <text class="cache-label">总缓存大小</text>
        <text class="cache-size total">{{totalCache}}</text>
      </view>
    </view>
    
    <view class="cache-actions">
      <van-button type="default" size="large" bindtap="clearImageCache">清理图片缓存</van-button>
      <van-button type="primary" size="large" bindtap="clearAllCache">清理全部缓存</van-button>
    </view>
  </view>
</van-popup>

<!-- 主题选择弹窗 -->
<van-popup 
  show="{{showThemeModal}}" 
  position="bottom" 
  round
  bind:close="hideThemeModal"
>
  <view class="theme-modal">
    <view class="modal-header">
      <text class="modal-title">选择主题</text>
      <van-icon name="cross" size="18" bindtap="hideThemeModal" />
    </view>
    
    <view class="theme-options">
      <view 
        class="theme-option {{settings.theme === item.value ? 'active' : ''}}"
        wx:for="{{themeOptions}}"
        wx:key="value"
        bindtap="selectTheme"
        data-theme="{{item.value}}"
      >
        <view class="theme-preview" style="background: {{item.color}}"></view>
        <text class="theme-name">{{item.label}}</text>
        <van-icon 
          name="success" 
          size="16" 
          color="#4CAF50" 
          wx:if="{{settings.theme === item.value}}"
        />
      </view>
    </view>
  </view>
</van-popup>

<!-- 语言选择弹窗 -->
<van-popup 
  show="{{showLanguageModal}}" 
  position="bottom" 
  round
  bind:close="hideLanguageModal"
>
  <view class="language-modal">
    <view class="modal-header">
      <text class="modal-title">选择语言</text>
      <van-icon name="cross" size="18" bindtap="hideLanguageModal" />
    </view>
    
    <view class="language-options">
      <view 
        class="language-option {{settings.language === item.value ? 'active' : ''}}"
        wx:for="{{languageOptions}}"
        wx:key="value"
        bindtap="selectLanguage"
        data-language="{{item.value}}"
      >
        <text class="language-name">{{item.label}}</text>
        <van-icon 
          name="success" 
          size="16" 
          color="#4CAF50" 
          wx:if="{{settings.language === item.value}}"
        />
      </view>
    </view>
  </view>
</van-popup>

<!-- 全局提示 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<van-notify id="van-notify" />
