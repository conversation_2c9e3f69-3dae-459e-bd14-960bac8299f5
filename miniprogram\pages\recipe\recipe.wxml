<!--菜谱列表页面-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <van-icon name="arrow-left" size="20" color="#333" />
        <text>返回</text>
      </view>
      <view class="navbar-title">推荐菜谱</view>
      <view class="navbar-right" bindtap="showFilter">
        <van-icon name="filter-o" size="20" color="#333" />
      </view>
    </view>
  </view>

  <!-- 食材信息展示 -->
  <view class="ingredients-header">
    <view class="header-content">
      <view class="ingredients-info">
        <van-icon name="apps-o" size="16" color="#4CAF50" />
        <text class="ingredients-text">基于食材：{{ingredientsText}}</text>
      </view>
      <view class="recipe-count">
        <text>共找到 {{recipes.length}} 道菜谱</text>
      </view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tags" wx:if="{{filterTags.length > 0}}">
    <scroll-view class="tags-scroll" scroll-x>
      <view class="tags-list">
        <view 
          class="tag-item {{item.active ? 'active' : ''}}" 
          wx:for="{{filterTags}}" 
          wx:key="key"
          bindtap="toggleFilter"
          data-key="{{item.key}}"
        >
          <text>{{item.name}}</text>
          <van-icon name="cross" size="12" wx:if="{{item.active}}" />
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 菜谱列表 -->
  <view class="recipes-section">
    <view class="recipes-list" wx:if="{{recipes.length > 0}}">
      <view 
        class="recipe-card" 
        wx:for="{{filteredRecipes}}" 
        wx:key="id"
        bindtap="viewRecipeDetail"
        data-recipe="{{item}}"
      >
        <!-- 菜谱图片 -->
        <view class="recipe-image-container">
          <image 
            class="recipe-image" 
            src="{{item.image || '/images/default-recipe.png'}}" 
            mode="aspectFill"
            lazy-load
          />
          <view class="image-overlay">
            <view class="difficulty-badge">
              <text>{{item.difficultyText}}</text>
            </view>
            <view class="cook-time-badge">
              <van-icon name="clock-o" size="12" color="white" />
              <text>{{item.cookTimeText}}</text>
            </view>
          </view>
        </view>

        <!-- 菜谱信息 -->
        <view class="recipe-info">
          <view class="recipe-header">
            <text class="recipe-name">{{item.name}}</text>
            <view class="recipe-rating" wx:if="{{item.rating}}">
              <van-icon name="star" size="14" color="#FFD700" />
              <text class="rating-text">{{item.rating}}</text>
            </view>
          </view>

          <view class="recipe-description" wx:if="{{item.description}}">
            <text>{{item.description}}</text>
          </view>

          <view class="recipe-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <text 
              class="tag" 
              wx:for="{{item.tags}}" 
              wx:for-item="tag" 
              wx:key="*this"
            >
              {{tag}}
            </text>
          </view>

          <view class="recipe-meta">
            <view class="meta-item">
              <van-icon name="friends-o" size="14" color="#666" />
              <text>{{item.servings || 2}}人份</text>
            </view>
            <view class="meta-item">
              <van-icon name="fire-o" size="14" color="#666" />
              <text>{{item.calories || 0}}卡</text>
            </view>
            <view class="meta-item">
              <van-icon name="thumb-circle-o" size="14" color="#666" />
              <text>{{item.likes || 0}}</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="recipe-actions">
          <view class="action-btn" bindtap="toggleFavorite" data-recipe="{{item}}" catchtap="true">
            <van-icon 
              name="{{item.isFavorite ? 'star' : 'star-o'}}" 
              size="18" 
              color="{{item.isFavorite ? '#FFD700' : '#999'}}" 
            />
          </view>
          <view class="action-btn" bindtap="shareRecipe" data-recipe="{{item}}" catchtap="true">
            <van-icon name="share-o" size="18" color="#999" />
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredRecipes.length === 0 && !loading}}">
      <van-empty 
        image="search" 
        description="{{recipes.length === 0 ? '暂无推荐菜谱' : '没有符合条件的菜谱'}}"
      >
        <van-button 
          type="primary" 
          size="small" 
          bindtap="resetFilters"
          wx:if="{{recipes.length > 0}}"
        >
          清除筛选
        </van-button>
        <van-button 
          type="primary" 
          size="small" 
          bindtap="goBack"
          wx:else
        >
          重新识别
        </van-button>
      </van-empty>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <van-loading type="spinner" size="24" color="#4CAF50" />
      <text>正在生成菜谱...</text>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <van-button 
      type="default" 
      size="large" 
      icon="photograph"
      bindtap="retakePhoto"
    >
      重新拍照
    </van-button>
    <van-button 
      type="primary" 
      size="large" 
      icon="shopping-cart-o"
      bindtap="createShoppingList"
      disabled="{{recipes.length === 0}}"
    >
      生成购物清单
    </van-button>
  </view>
</view>

<!-- 筛选弹窗 -->
<van-popup 
  show="{{showFilterModal}}" 
  position="bottom" 
  round
  bind:close="hideFilter"
>
  <view class="filter-modal">
    <view class="modal-header">
      <text class="modal-title">筛选菜谱</text>
      <van-icon name="cross" size="18" bindtap="hideFilter" />
    </view>
    
    <view class="filter-content">
      <!-- 难度筛选 -->
      <view class="filter-group">
        <text class="group-title">难度</text>
        <view class="filter-options">
          <view 
            class="option-item {{filters.difficulty === item.value ? 'active' : ''}}"
            wx:for="{{difficultyOptions}}" 
            wx:key="value"
            bindtap="selectFilter"
            data-type="difficulty"
            data-value="{{item.value}}"
          >
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>

      <!-- 制作时间筛选 -->
      <view class="filter-group">
        <text class="group-title">制作时间</text>
        <view class="filter-options">
          <view 
            class="option-item {{filters.cookTime === item.value ? 'active' : ''}}"
            wx:for="{{cookTimeOptions}}" 
            wx:key="value"
            bindtap="selectFilter"
            data-type="cookTime"
            data-value="{{item.value}}"
          >
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>

      <!-- 菜系筛选 -->
      <view class="filter-group">
        <text class="group-title">菜系</text>
        <view class="filter-options">
          <view 
            class="option-item {{filters.cuisine === item.value ? 'active' : ''}}"
            wx:for="{{cuisineOptions}}" 
            wx:key="value"
            bindtap="selectFilter"
            data-type="cuisine"
            data-value="{{item.value}}"
          >
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="filter-actions">
      <van-button type="default" bindtap="resetFilters">重置</van-button>
      <van-button type="primary" bindtap="applyFilters">确定</van-button>
    </view>
  </view>
</van-popup>

<!-- 全局提示 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<van-notify id="van-notify" />
