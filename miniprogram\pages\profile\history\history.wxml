<!--历史记录页面-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <van-icon name="arrow-left" size="20" color="#333" />
      </view>
      <view class="navbar-title">历史记录</view>
      <view class="navbar-right" bindtap="toggleEditMode">
        <text class="edit-btn">{{editMode ? '完成' : '编辑'}}</text>
      </view>
    </view>
  </view>

  <!-- 统计信息和筛选 -->
  <view class="stats-bar">
    <text class="stats-text">共 {{historyList.length}} 条记录</text>
    <view class="stats-actions" wx:if="{{historyList.length > 0}}">
      <text class="action-btn" bindtap="filterHistory">
        <van-icon name="filter-o" size="14" />
        筛选
      </text>
      <text class="action-btn" bindtap="searchHistory">
        <van-icon name="search" size="14" />
        搜索
      </text>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar" wx:if="{{showSearch}}">
    <van-search
      value="{{searchKeyword}}"
      placeholder="搜索历史记录"
      bind:search="onSearch"
      bind:change="onSearchChange"
      bind:cancel="hideSearch"
      show-action
    />
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tabs" wx:if="{{showFilter}}">
    <scroll-view class="tabs-scroll" scroll-x>
      <view class="tab-list">
        <text 
          class="tab-item {{filterType === item.value ? 'active' : ''}}"
          wx:for="{{filterOptions}}"
          wx:key="value"
          bindtap="selectFilter"
          data-type="{{item.value}}"
        >
          {{item.label}}
        </text>
      </view>
    </scroll-view>
  </view>

  <!-- 历史记录列表 -->
  <view class="history-list" wx:if="{{displayList.length > 0}}">
    <!-- 按日期分组显示 -->
    <view 
      class="date-group" 
      wx:for="{{groupedList}}" 
      wx:key="date"
    >
      <view class="date-header">
        <text class="date-text">{{item.dateText}}</text>
        <text class="count-text">{{item.records.length}}条</text>
      </view>
      
      <view 
        class="history-item {{editMode ? 'edit-mode' : ''}}"
        wx:for="{{item.records}}"
        wx:key="id"
        wx:for-item="record"
        bindtap="viewRecord"
        data-record="{{record}}"
      >
        <!-- 选择框 -->
        <view class="item-checkbox" wx:if="{{editMode}}" bindtap="toggleSelect" data-id="{{record.id}}">
          <van-icon 
            name="{{record.selected ? 'checked' : 'circle'}}" 
            size="20" 
            color="{{record.selected ? '#4CAF50' : '#ccc'}}" 
          />
        </view>

        <!-- 记录图标 -->
        <view class="record-icon">
          <van-icon 
            name="{{record.typeIcon}}" 
            size="24" 
            color="{{record.typeColor}}" 
          />
        </view>

        <!-- 记录信息 -->
        <view class="record-info">
          <text class="record-title">{{record.title}}</text>
          <text class="record-desc">{{record.description}}</text>
          <view class="record-meta">
            <text class="meta-item">{{record.timeText}}</text>
            <text class="meta-item" wx:if="{{record.result}}">{{record.result}}</text>
          </view>
        </view>

        <!-- 缩略图 -->
        <image 
          class="record-thumb" 
          src="{{record.thumbnail}}" 
          mode="aspectFill"
          wx:if="{{record.thumbnail}}"
          lazy-load
        />

        <!-- 操作按钮 -->
        <view class="item-actions" wx:if="{{!editMode}}">
          <view class="action-btn" bindtap="shareRecord" data-record="{{record}}">
            <van-icon name="share-o" size="16" color="#666" />
          </view>
          <view class="action-btn" bindtap="deleteRecord" data-id="{{record.id}}">
            <van-icon name="delete-o" size="16" color="#ff4444" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{displayList.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-history.png" mode="aspectFit" />
    <text class="empty-title">{{searchKeyword ? '没有找到相关记录' : '还没有使用记录'}}</text>
    <text class="empty-desc">{{searchKeyword ? '试试其他关键词' : '开始使用智能识别功能吧'}}</text>
    <van-button 
      type="primary" 
      size="small" 
      bindtap="goCamera"
      wx:if="{{!searchKeyword}}"
    >
      开始识别
    </van-button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <van-loading size="24" color="#4CAF50">加载中...</van-loading>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{editMode && historyList.length > 0}}">
    <view class="select-all" bindtap="toggleSelectAll">
      <van-icon 
        name="{{allSelected ? 'checked' : 'circle'}}" 
        size="18" 
        color="{{allSelected ? '#4CAF50' : '#ccc'}}" 
      />
      <text>全选</text>
    </view>
    
    <view class="action-buttons">
      <van-button 
        type="default" 
        size="small" 
        bindtap="batchExport"
        disabled="{{selectedCount === 0}}"
      >
        导出 ({{selectedCount}})
      </van-button>
      <van-button 
        type="danger" 
        size="small" 
        bindtap="batchDelete"
        disabled="{{selectedCount === 0}}"
      >
        删除 ({{selectedCount}})
      </van-button>
    </view>
  </view>
</view>

<!-- 筛选弹窗 -->
<van-popup 
  show="{{showFilterModal}}" 
  position="bottom" 
  round
  bind:close="hideFilterModal"
>
  <view class="filter-modal">
    <view class="modal-header">
      <text class="modal-title">筛选条件</text>
      <van-icon name="cross" size="18" bindtap="hideFilterModal" />
    </view>
    
    <view class="filter-content">
      <view class="filter-section">
        <text class="section-title">记录类型</text>
        <view class="option-list">
          <view 
            class="option-item {{filterType === item.value ? 'active' : ''}}"
            wx:for="{{filterOptions}}"
            wx:key="value"
            bindtap="selectFilter"
            data-type="{{item.value}}"
          >
            <text class="option-text">{{item.label}}</text>
            <van-icon 
              name="success" 
              size="16" 
              color="#4CAF50" 
              wx:if="{{filterType === item.value}}"
            />
          </view>
        </view>
      </view>
      
      <view class="filter-section">
        <text class="section-title">时间范围</text>
        <view class="option-list">
          <view 
            class="option-item {{timeRange === item.value ? 'active' : ''}}"
            wx:for="{{timeRangeOptions}}"
            wx:key="value"
            bindtap="selectTimeRange"
            data-range="{{item.value}}"
          >
            <text class="option-text">{{item.label}}</text>
            <van-icon 
              name="success" 
              size="16" 
              color="#4CAF50" 
              wx:if="{{timeRange === item.value}}"
            />
          </view>
        </view>
      </view>
    </view>
    
    <view class="filter-actions">
      <van-button type="default" size="large" bindtap="resetFilter">重置</van-button>
      <van-button type="primary" size="large" bindtap="applyFilter">确定</van-button>
    </view>
  </view>
</van-popup>

<!-- 全局提示 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<van-notify id="van-notify" />
