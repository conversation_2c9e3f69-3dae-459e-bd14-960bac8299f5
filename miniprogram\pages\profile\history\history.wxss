/* 历史记录页面样式 */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.edit-btn {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 500;
}

/* 统计信息栏 */
.stats-bar {
  margin-top: calc(88rpx + var(--status-bar-height, 44rpx) + 20rpx);
  padding: 24rpx 32rpx;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.stats-text {
  font-size: 28rpx;
  color: #666;
}

.stats-actions {
  display: flex;
  gap: 32rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: #f8f8f8;
  transition: all 0.3s ease;
}

.action-btn:active {
  background: #e8e8e8;
  transform: scale(0.95);
}

/* 搜索栏 */
.search-bar {
  background: white;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #eee;
}

/* 筛选标签 */
.filter-tabs {
  background: white;
  border-bottom: 1rpx solid #eee;
}

.tabs-scroll {
  white-space: nowrap;
}

.tab-list {
  display: flex;
  padding: 20rpx 32rpx;
  gap: 24rpx;
}

.tab-item {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  background: #f8f8f8;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #4CAF50;
  background: #e8f5e8;
  font-weight: 500;
}

/* 历史记录列表 */
.history-list {
  padding: 20rpx 32rpx;
}

.date-group {
  margin-bottom: 40rpx;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  margin-bottom: 16rpx;
}

.date-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.count-text {
  font-size: 24rpx;
  color: #999;
}

.history-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
}

.history-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.history-item.edit-mode {
  padding-left: 60rpx;
}

/* 选择框 */
.item-checkbox {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 记录图标 */
.record-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f0f8f0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  margin-right: 20rpx;
}

/* 记录信息 */
.record-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.record-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.record-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.record-meta {
  display: flex;
  gap: 16rpx;
}

.meta-item {
  font-size: 22rpx;
  color: #999;
}

/* 缩略图 */
.record-thumb {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background: #f0f0f0;
  flex-shrink: 0;
  margin-left: 16rpx;
}

/* 操作按钮 */
.item-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-left: 16rpx;
}

.item-actions .action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f8f8f8;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  gap: 0;
}

.item-actions .action-btn:active {
  background: #e8e8e8;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 50;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 28rpx;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

/* 筛选弹窗 */
.filter-modal {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 0;
  max-height: 80vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.filter-content {
  padding: 30rpx 0;
  max-height: 50vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  padding: 0 40rpx 20rpx;
}

.option-list {
  padding: 0 40rpx;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background 0.3s ease;
}

.option-item:active {
  background: #f8f8f8;
}

.option-item.active {
  background: #f0f8f0;
}

.option-text {
  font-size: 30rpx;
  color: #333;
}

.option-item.active .option-text {
  color: #4CAF50;
  font-weight: 500;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx 40rpx 0;
  border-top: 1rpx solid #eee;
}
