// 收藏页面逻辑
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import Notify from '@vant/weapp/notify/notify';
import { get, post, del } from '../../../utils/api';
import { getStorage, setStorage, CACHE_KEYS } from '../../../utils/storage';
import { formatTime } from '../../../utils/format';

Page({
  data: {
    // 收藏列表数据
    favoriteList: [],
    displayList: [],
    
    // UI状态
    loading: false,
    editMode: false,
    showSearch: false,
    showSortModal: false,
    
    // 搜索相关
    searchKeyword: '',
    
    // 排序相关
    sortType: 'time', // time, name, rating
    sortOptions: [
      { label: '收藏时间', value: 'time' },
      { label: '菜谱名称', value: 'name' },
      { label: '评分高低', value: 'rating' }
    ],
    
    // 选择相关
    selectedCount: 0,
    allSelected: false
  },

  onLoad() {
    console.log('Favorites page loaded');
    this.loadFavorites();
  },

  onShow() {
    // 每次显示时刷新数据
    this.loadFavorites();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 加载收藏列表
  async loadFavorites() {
    try {
      this.setData({ loading: true });
      
      // 先从本地缓存获取
      const cachedFavorites = getStorage(CACHE_KEYS.USER_FAVORITES, []);
      if (cachedFavorites.length > 0) {
        this.processFavoriteList(cachedFavorites);
      }
      
      // 从服务器获取最新数据
      const response = await get('/api/users/favorites');
      if (response.success) {
        const favoriteList = response.data.map(item => ({
          ...item,
          selected: false,
          addTimeText: formatTime(item.createdAt, 'MM-DD HH:mm'),
          cookTimeText: this.formatCookTime(item.cookTime),
          difficultyText: this.formatDifficulty(item.difficulty)
        }));
        
        // 缓存到本地
        setStorage(CACHE_KEYS.USER_FAVORITES, favoriteList);
        this.processFavoriteList(favoriteList);
      }
    } catch (error) {
      console.error('Load favorites failed:', error);
      Toast.fail('加载失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 处理收藏列表数据
  processFavoriteList(favoriteList) {
    this.setData({ 
      favoriteList,
      displayList: this.filterAndSortList(favoriteList)
    });
  },

  // 过滤和排序列表
  filterAndSortList(list) {
    let filteredList = [...list];
    
    // 搜索过滤
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filteredList = filteredList.filter(item => 
        item.name.toLowerCase().includes(keyword) ||
        (item.ingredients && item.ingredients.some(ing => 
          ing.name.toLowerCase().includes(keyword)
        ))
      );
    }
    
    // 排序
    const { sortType } = this.data;
    filteredList.sort((a, b) => {
      switch (sortType) {
        case 'time':
          return new Date(b.createdAt) - new Date(a.createdAt);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        default:
          return 0;
      }
    });
    
    return filteredList;
  },

  // 切换编辑模式
  toggleEditMode() {
    const editMode = !this.data.editMode;
    this.setData({ 
      editMode,
      selectedCount: 0,
      allSelected: false
    });
    
    // 重置选择状态
    if (!editMode) {
      const favoriteList = this.data.favoriteList.map(item => ({
        ...item,
        selected: false
      }));
      this.setData({ 
        favoriteList,
        displayList: this.filterAndSortList(favoriteList)
      });
    }
  },

  // 显示搜索栏
  searchFavorites() {
    this.setData({ showSearch: true });
  },

  // 隐藏搜索栏
  hideSearch() {
    this.setData({ 
      showSearch: false,
      searchKeyword: ''
    });
    this.refreshDisplayList();
  },

  // 搜索输入变化
  onSearchChange(e) {
    this.setData({ searchKeyword: e.detail });
    this.refreshDisplayList();
  },

  // 执行搜索
  onSearch(e) {
    this.setData({ searchKeyword: e.detail });
    this.refreshDisplayList();
  },

  // 显示排序弹窗
  sortFavorites() {
    this.setData({ showSortModal: true });
  },

  // 隐藏排序弹窗
  hideSortModal() {
    this.setData({ showSortModal: false });
  },

  // 选择排序方式
  selectSort(e) {
    const sortType = e.currentTarget.dataset.type;
    this.setData({ 
      sortType,
      showSortModal: false
    });
    this.refreshDisplayList();
  },

  // 刷新显示列表
  refreshDisplayList() {
    const displayList = this.filterAndSortList(this.data.favoriteList);
    this.setData({ displayList });
  },

  // 查看菜谱详情
  viewRecipe(e) {
    if (this.data.editMode) return;
    
    const recipe = e.currentTarget.dataset.recipe;
    wx.navigateTo({
      url: `/pages/recipe/detail/detail?id=${recipe.id}`
    });
  },

  // 切换选择状态
  toggleSelect(e) {
    e.stopPropagation();
    const id = e.currentTarget.dataset.id;
    
    const favoriteList = this.data.favoriteList.map(item => {
      if (item.id === id) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    
    const selectedCount = favoriteList.filter(item => item.selected).length;
    const allSelected = selectedCount === favoriteList.length;
    
    this.setData({ 
      favoriteList,
      displayList: this.filterAndSortList(favoriteList),
      selectedCount,
      allSelected
    });
  },

  // 全选/取消全选
  toggleSelectAll() {
    const allSelected = !this.data.allSelected;
    const favoriteList = this.data.favoriteList.map(item => ({
      ...item,
      selected: allSelected
    }));
    
    const selectedCount = allSelected ? favoriteList.length : 0;
    
    this.setData({ 
      favoriteList,
      displayList: this.filterAndSortList(favoriteList),
      selectedCount,
      allSelected
    });
  },

  // 分享菜谱
  shareRecipe(e) {
    e.stopPropagation();
    const recipe = e.currentTarget.dataset.recipe;
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    // 这里可以添加分享逻辑
    Toast.success('分享功能开发中');
  },

  // 移除收藏
  async removeFavorite(e) {
    e.stopPropagation();
    const id = e.currentTarget.dataset.id;
    
    try {
      const result = await Dialog.confirm({
        title: '确认删除',
        message: '确定要取消收藏这个菜谱吗？'
      });
      
      if (result) {
        await this.deleteFavoriteById(id);
      }
    } catch (error) {
      // 用户取消
    }
  },

  // 批量分享
  batchShare() {
    if (this.data.selectedCount === 0) return;
    
    Toast.success('批量分享功能开发中');
  },

  // 批量删除
  async batchDelete() {
    if (this.data.selectedCount === 0) return;
    
    try {
      const result = await Dialog.confirm({
        title: '确认删除',
        message: `确定要删除选中的 ${this.data.selectedCount} 个收藏吗？`
      });
      
      if (result) {
        const selectedIds = this.data.favoriteList
          .filter(item => item.selected)
          .map(item => item.id);
        
        await this.deleteFavoritesByIds(selectedIds);
      }
    } catch (error) {
      // 用户取消
    }
  },

  // 删除单个收藏
  async deleteFavoriteById(id) {
    try {
      const response = await del(`/api/users/favorites/${id}`);
      if (response.success) {
        const favoriteList = this.data.favoriteList.filter(item => item.id !== id);
        setStorage(CACHE_KEYS.USER_FAVORITES, favoriteList);
        this.processFavoriteList(favoriteList);
        Toast.success('已取消收藏');
      }
    } catch (error) {
      console.error('Delete favorite failed:', error);
      Toast.fail('删除失败，请重试');
    }
  },

  // 批量删除收藏
  async deleteFavoritesByIds(ids) {
    try {
      const response = await post('/api/users/favorites/batch-delete', { ids });
      if (response.success) {
        const favoriteList = this.data.favoriteList.filter(item => !ids.includes(item.id));
        setStorage(CACHE_KEYS.USER_FAVORITES, favoriteList);
        this.processFavoriteList(favoriteList);
        this.setData({ 
          editMode: false,
          selectedCount: 0,
          allSelected: false
        });
        Toast.success(`已删除 ${ids.length} 个收藏`);
      }
    } catch (error) {
      console.error('Batch delete favorites failed:', error);
      Toast.fail('删除失败，请重试');
    }
  },

  // 去发现页面
  goDiscover() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 格式化烹饪时间
  formatCookTime(minutes) {
    if (!minutes) return '30分钟';
    if (minutes < 60) return `${minutes}分钟`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
  },

  // 格式化难度
  formatDifficulty(level) {
    const difficultyMap = {
      1: '简单',
      2: '中等',
      3: '困难'
    };
    return difficultyMap[level] || '简单';
  }
});
