// 认证工具类
import { post } from './api';
import { setStorage, getStorage, removeStorage, USER_KEYS } from './storage';
import Toast from '@vant/weapp/toast/toast';

/**
 * 认证工具类
 */
class AuthUtils {
  
  /**
   * 微信登录
   * @returns {Promise<Object>} 登录结果
   */
  async wxLogin() {
    try {
      // 获取微信登录code
      const loginRes = await this.getWxLoginCode();
      
      // 调用后端登录接口
      const result = await post('/api/auth/wx-login', {
        code: loginRes.code
      }, {
        showLoading: true,
        loadingText: '登录中...'
      });

      // 保存登录信息
      if (result.success) {
        this.saveLoginInfo(result.data);
        return result.data;
      } else {
        throw new Error(result.message || '登录失败');
      }
    } catch (error) {
      console.error('WeChat login failed:', error);
      Toast.fail(error.message || '登录失败');
      throw error;
    }
  }

  /**
   * 获取微信登录code
   * @returns {Promise<Object>} 登录code
   */
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 获取用户信息
   * @returns {Promise<Object>} 用户信息
   */
  async getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 保存登录信息
   * @param {Object} loginData 登录数据
   */
  saveLoginInfo(loginData) {
    const { token, userInfo, expiresIn } = loginData;
    
    // 保存token（设置过期时间）
    if (token) {
      const expire = expiresIn ? expiresIn : 7 * 24 * 60 * 60; // 默认7天
      setStorage(USER_KEYS.TOKEN, token, expire);
    }
    
    // 保存用户信息
    if (userInfo) {
      setStorage(USER_KEYS.USER_INFO, userInfo);
    }
  }

  /**
   * 获取当前token
   * @returns {string|null} token
   */
  getToken() {
    return getStorage(USER_KEYS.TOKEN);
  }

  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息
   */
  getUserInfo() {
    return getStorage(USER_KEYS.USER_INFO);
  }

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    const token = this.getToken();
    const userInfo = this.getUserInfo();
    return !!(token && userInfo);
  }

  /**
   * 退出登录
   */
  logout() {
    // 清除本地存储
    removeStorage(USER_KEYS.TOKEN);
    removeStorage(USER_KEYS.USER_INFO);
    removeStorage(USER_KEYS.USER_PREFERENCES);
    
    // 显示提示
    Toast.success('已退出登录');
    
    // 跳转到首页
    wx.reLaunch({
      url: '/pages/index/index'
    });
  }

  /**
   * 检查登录状态并自动登录
   * @returns {Promise<boolean>} 是否登录成功
   */
  async checkAndAutoLogin() {
    try {
      // 如果已经有有效的登录信息，直接返回
      if (this.isLoggedIn()) {
        return true;
      }

      // 尝试静默登录
      await this.wxLogin();
      return true;
    } catch (error) {
      console.error('Auto login failed:', error);
      return false;
    }
  }

  /**
   * 更新用户信息
   * @param {Object} userInfo 用户信息
   * @returns {Promise<Object>} 更新结果
   */
  async updateUserInfo(userInfo) {
    try {
      const result = await post('/api/user/update', userInfo, {
        showLoading: true,
        loadingText: '更新中...'
      });

      if (result.success) {
        // 更新本地存储
        const currentUserInfo = this.getUserInfo() || {};
        const newUserInfo = { ...currentUserInfo, ...result.data };
        setStorage(USER_KEYS.USER_INFO, newUserInfo);
        
        Toast.success('更新成功');
        return result.data;
      } else {
        throw new Error(result.message || '更新失败');
      }
    } catch (error) {
      console.error('Update user info failed:', error);
      Toast.fail(error.message || '更新失败');
      throw error;
    }
  }

  /**
   * 获取用户偏好设置
   * @returns {Object} 用户偏好
   */
  getUserPreferences() {
    return getStorage(USER_KEYS.USER_PREFERENCES, {
      // 默认偏好设置
      difficulty: 'medium', // 菜谱难度偏好: easy, medium, hard
      cookTime: 30, // 制作时间偏好(分钟)
      cuisine: [], // 菜系偏好
      dietary: [], // 饮食限制: vegetarian, vegan, gluten-free, etc.
      spiciness: 'medium', // 辣度偏好: mild, medium, spicy
      notifications: {
        recipe: true, // 菜谱推荐通知
        update: true, // 更新通知
        promotion: false // 推广通知
      }
    });
  }

  /**
   * 保存用户偏好设置
   * @param {Object} preferences 偏好设置
   */
  saveUserPreferences(preferences) {
    const currentPreferences = this.getUserPreferences();
    const newPreferences = { ...currentPreferences, ...preferences };
    setStorage(USER_KEYS.USER_PREFERENCES, newPreferences);
  }

  /**
   * 检查权限
   * @param {string} scope 权限范围
   * @returns {Promise<boolean>} 是否有权限
   */
  async checkPermission(scope) {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          resolve(res.authSetting[scope] === true);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }

  /**
   * 请求权限
   * @param {string} scope 权限范围
   * @returns {Promise<boolean>} 是否授权成功
   */
  async requestPermission(scope) {
    return new Promise((resolve) => {
      wx.authorize({
        scope,
        success: () => {
          resolve(true);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }

  /**
   * 检查并请求权限
   * @param {string} scope 权限范围
   * @param {string} tip 权限说明
   * @returns {Promise<boolean>} 是否有权限
   */
  async ensurePermission(scope, tip) {
    // 先检查是否已有权限
    const hasPermission = await this.checkPermission(scope);
    if (hasPermission) {
      return true;
    }

    // 尝试请求权限
    const granted = await this.requestPermission(scope);
    if (granted) {
      return true;
    }

    // 权限被拒绝，提示用户手动开启
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '权限申请',
        content: tip || '需要相关权限才能正常使用功能',
        confirmText: '去设置',
        cancelText: '取消',
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });

    if (result) {
      // 打开设置页面
      wx.openSetting();
    }

    return false;
  }
}

// 创建实例
const auth = new AuthUtils();

// 导出常用方法
export const wxLogin = auth.wxLogin.bind(auth);
export const getUserProfile = auth.getUserProfile.bind(auth);
export const getToken = auth.getToken.bind(auth);
export const getUserInfo = auth.getUserInfo.bind(auth);
export const isLoggedIn = auth.isLoggedIn.bind(auth);
export const logout = auth.logout.bind(auth);
export const checkAndAutoLogin = auth.checkAndAutoLogin.bind(auth);
export const updateUserInfo = auth.updateUserInfo.bind(auth);
export const getUserPreferences = auth.getUserPreferences.bind(auth);
export const saveUserPreferences = auth.saveUserPreferences.bind(auth);
export const checkPermission = auth.checkPermission.bind(auth);
export const requestPermission = auth.requestPermission.bind(auth);
export const ensurePermission = auth.ensurePermission.bind(auth);

export default auth;
