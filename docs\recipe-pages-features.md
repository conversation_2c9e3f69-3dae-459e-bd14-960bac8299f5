# 菜谱页面功能说明

## 页面概述

菜谱页面包含菜谱列表页面和菜谱详情页面，是AI智能菜谱生成小程序的核心展示模块，用户可以浏览推荐菜谱、查看详细制作步骤、收藏喜欢的菜谱等。

## 菜谱列表页面 (recipe.*)

### 主要功能

#### 1. 菜谱展示
- **基于食材推荐**: 根据识别的食材智能推荐相关菜谱
- **卡片式布局**: 美观的菜谱卡片展示，包含图片、名称、时间、难度等信息
- **营养标签**: 显示卡路里、人份数、点赞数等关键信息
- **评分显示**: 显示菜谱评分和用户反馈

#### 2. 筛选功能
- **多维度筛选**: 支持按难度、制作时间、菜系进行筛选
- **筛选标签**: 直观的筛选标签显示，支持快速取消
- **实时筛选**: 筛选条件变化时实时更新菜谱列表
- **筛选状态保持**: 记住用户的筛选偏好

#### 3. 交互功能
- **收藏功能**: 一键收藏/取消收藏菜谱
- **分享功能**: 支持分享菜谱到微信好友或朋友圈
- **快速预览**: 点击菜谱卡片快速查看详情
- **购物清单**: 一键生成所有菜谱的购物清单

#### 4. 用户体验
- **加载状态**: 清晰的加载提示和空状态处理
- **下拉刷新**: 支持下拉刷新获取最新推荐
- **无限滚动**: 支持滚动加载更多菜谱
- **操作反馈**: 完善的操作成功/失败提示

### 技术实现

#### 页面结构 (recipe.wxml)
```xml
- 自定义导航栏
  - 返回按钮
  - 页面标题
  - 筛选按钮

- 食材信息头部
  - 基础食材展示
  - 菜谱数量统计

- 筛选标签栏
  - 活跃筛选条件展示
  - 快速取消筛选

- 菜谱列表
  - 菜谱卡片
    - 菜谱图片和信息覆盖层
    - 菜谱基本信息
    - 标签和元数据
    - 操作按钮

- 底部操作栏
  - 重新拍照按钮
  - 生成购物清单按钮

- 筛选弹窗
  - 难度选择
  - 时间选择
  - 菜系选择
```

#### 样式设计 (recipe.wxss)
- **卡片设计**: 现代化的卡片布局，带阴影和圆角
- **图片处理**: 支持懒加载和占位图
- **响应式布局**: 适配不同屏幕尺寸
- **动画效果**: 平滑的交互动画和状态转换
- **色彩系统**: 统一的色彩规范和主题色

#### 逻辑实现 (recipe.js)
- **数据管理**: 菜谱数据的获取、处理和状态管理
- **筛选逻辑**: 多条件筛选算法和标签生成
- **收藏管理**: 本地收藏状态同步和API调用
- **分享功能**: 微信分享配置和内容生成
- **购物清单**: 食材合并算法和清单生成

## 菜谱详情页面 (detail.*)

### 主要功能

#### 1. 菜谱信息展示
- **头图展示**: 高质量菜谱图片，支持预览放大
- **基本信息**: 菜谱名称、描述、制作时间、难度、人份数
- **评分信息**: 用户评分和评价统计
- **标签系统**: 菜系、口味、特色标签

#### 2. 营养信息
- **营养成分**: 详细的营养成分分析
- **卡路里计算**: 每份卡路里和营养价值
- **健康提示**: 营养建议和健康提醒
- **图标化展示**: 直观的营养信息图标

#### 3. 食材清单
- **完整清单**: 详细的食材名称、用量、单位
- **分类展示**: 按食材类型分类显示
- **购物功能**: 一键添加到购物清单
- **替代建议**: 食材替代方案提示

#### 4. 制作步骤
- **分步指导**: 详细的制作步骤说明
- **步骤图片**: 关键步骤的示意图片
- **烹饪技巧**: 专业的烹饪技巧和注意事项
- **时间提醒**: 每个步骤的预计时间

#### 5. 制作模式
- **分步制作**: 逐步指导模式，防止遗漏
- **定时提醒**: 烹饪时间提醒功能
- **语音播报**: 解放双手的语音指导
- **进度跟踪**: 制作进度记录和恢复

#### 6. 相关推荐
- **相似菜谱**: 基于当前菜谱推荐相似菜谱
- **同食材菜谱**: 使用相同食材的其他菜谱
- **用户偏好**: 基于用户历史偏好的推荐
- **热门推荐**: 热门和高评分菜谱推荐

### 技术实现

#### 页面结构 (detail.wxml)
```xml
- 自定义导航栏
  - 返回按钮
  - 菜谱标题
  - 收藏和分享按钮

- 菜谱头图
  - 高质量图片展示
  - 信息覆盖层
  - 基本信息展示

- 内容区域
  - 营养信息网格
  - 食材清单列表
  - 制作步骤列表
  - 烹饪技巧提示
  - 相关推荐滚动

- 底部操作栏
  - 开始制作按钮
  - 评价反馈按钮

- 制作模式弹窗
  - 分步制作选项
  - 定时提醒选项
```

#### 样式设计 (detail.wxss)
- **沉浸式设计**: 全屏头图和透明导航栏
- **内容层次**: 清晰的信息层次和视觉引导
- **步骤设计**: 直观的步骤编号和内容布局
- **图片处理**: 支持图片预览和懒加载
- **交互反馈**: 丰富的交互动画和状态反馈

#### 逻辑实现 (detail.js)
- **数据加载**: 菜谱详情数据的异步加载
- **收藏管理**: 收藏状态的本地和远程同步
- **分享功能**: 动态分享内容生成
- **制作模式**: 制作辅助功能的实现
- **相关推荐**: 推荐算法和数据获取

## 数据流转

### 1. 页面间数据传递
```
识别结果页面 → 菜谱列表页面
- 传递食材信息和生成的菜谱数据
- 使用URL参数传递JSON数据

菜谱列表页面 → 菜谱详情页面
- 传递菜谱ID
- 详情页面根据ID加载完整数据
```

### 2. 本地存储管理
```
收藏数据: FAVORITE_RECIPES
- 存储用户收藏的菜谱信息
- 支持离线访问和同步

浏览历史: VIEW_HISTORY
- 记录用户浏览的菜谱历史
- 支持快速访问最近浏览

筛选偏好: FILTER_PREFERENCES
- 保存用户的筛选偏好
- 提升用户体验
```

### 3. API接口调用
```
菜谱列表: GET /api/recipes
菜谱详情: GET /api/recipes/:id
相关推荐: GET /api/recipes/:id/related
收藏操作: POST /api/user/favorites
购物清单: POST /api/user/shopping-list
```

## 性能优化

### 1. 图片优化
- **懒加载**: 菜谱图片懒加载，减少初始加载时间
- **占位图**: 使用默认占位图，提升用户体验
- **压缩处理**: 服务端图片压缩和CDN加速
- **缓存策略**: 图片缓存策略，减少重复加载

### 2. 数据优化
- **分页加载**: 菜谱列表分页加载，避免一次性加载过多数据
- **缓存机制**: 菜谱数据本地缓存，减少网络请求
- **预加载**: 关键数据预加载，提升响应速度
- **增量更新**: 数据增量更新，减少传输量

### 3. 交互优化
- **骨架屏**: 使用骨架屏提升加载体验
- **防抖处理**: 搜索和筛选操作防抖处理
- **状态管理**: 合理的状态管理，避免不必要的重渲染
- **动画优化**: 流畅的动画效果，提升交互体验

## 错误处理

### 1. 网络错误
- **超时处理**: 网络请求超时重试机制
- **离线提示**: 网络断开时的友好提示
- **数据恢复**: 网络恢复后的数据自动恢复
- **缓存降级**: 网络异常时使用缓存数据

### 2. 数据错误
- **空状态**: 无数据时的空状态展示
- **格式验证**: 数据格式验证和容错处理
- **默认值**: 关键字段的默认值设置
- **错误边界**: 组件级错误边界处理

### 3. 用户操作错误
- **权限检查**: 操作权限验证和提示
- **重复操作**: 防止重复提交和操作
- **输入验证**: 用户输入的验证和提示
- **操作确认**: 重要操作的二次确认

## 可访问性

### 1. 视觉辅助
- **对比度**: 确保足够的颜色对比度
- **字体大小**: 支持系统字体大小设置
- **色彩标识**: 不仅依赖颜色传达信息
- **焦点指示**: 清晰的焦点指示器

### 2. 操作辅助
- **触摸目标**: 足够大的触摸目标区域
- **手势支持**: 支持常见的手势操作
- **语音反馈**: 重要操作的语音反馈
- **简化操作**: 简化复杂的操作流程

## 后续优化方向

### 1. 功能增强
- **个性化推荐**: 基于用户行为的个性化推荐算法
- **社交功能**: 用户评论、评分、分享功能
- **离线模式**: 支持离线浏览收藏的菜谱
- **多媒体**: 支持视频教程和语音指导

### 2. 体验优化
- **智能搜索**: 支持语音搜索和图像搜索
- **AR功能**: AR辅助制作和食材识别
- **个性化**: 根据用户偏好定制界面和内容
- **国际化**: 支持多语言和地区化内容

### 3. 技术升级
- **性能监控**: 实时性能监控和优化
- **A/B测试**: 功能和界面的A/B测试
- **数据分析**: 用户行为数据分析和洞察
- **智能化**: 更智能的推荐和个性化算法

---

**文档创建时间**: 2024-12-19  
**最后更新**: 2024-12-19  
**版本**: v1.0
