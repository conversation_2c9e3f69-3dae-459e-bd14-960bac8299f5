<!--收藏页面-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <van-icon name="arrow-left" size="20" color="#333" />
      </view>
      <view class="navbar-title">我的收藏</view>
      <view class="navbar-right" bindtap="toggleEditMode">
        <text class="edit-btn">{{editMode ? '完成' : '编辑'}}</text>
      </view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-bar">
    <text class="stats-text">共收藏 {{favoriteList.length}} 个菜谱</text>
    <view class="stats-actions" wx:if="{{favoriteList.length > 0}}">
      <text class="action-btn" bindtap="sortFavorites">
        <van-icon name="sort" size="14" />
        排序
      </text>
      <text class="action-btn" bindtap="searchFavorites">
        <van-icon name="search" size="14" />
        搜索
      </text>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar" wx:if="{{showSearch}}">
    <van-search
      value="{{searchKeyword}}"
      placeholder="搜索收藏的菜谱"
      bind:search="onSearch"
      bind:change="onSearchChange"
      bind:cancel="hideSearch"
      show-action
    />
  </view>

  <!-- 收藏列表 -->
  <view class="favorites-list" wx:if="{{displayList.length > 0}}">
    <view 
      class="favorite-item {{editMode ? 'edit-mode' : ''}}" 
      wx:for="{{displayList}}" 
      wx:key="id"
      bindtap="viewRecipe"
      data-recipe="{{item}}"
    >
      <!-- 选择框 -->
      <view class="item-checkbox" wx:if="{{editMode}}" bindtap="toggleSelect" data-id="{{item.id}}">
        <van-icon 
          name="{{item.selected ? 'checked' : 'circle'}}" 
          size="20" 
          color="{{item.selected ? '#4CAF50' : '#ccc'}}" 
        />
      </view>

      <!-- 菜谱图片 -->
      <image 
        class="recipe-image" 
        src="{{item.image || '/images/default-recipe.png'}}" 
        mode="aspectFill"
        lazy-load
      />

      <!-- 菜谱信息 -->
      <view class="recipe-info">
        <text class="recipe-name">{{item.name}}</text>
        <view class="recipe-meta">
          <text class="meta-item">
            <van-icon name="clock-o" size="12" color="#999" />
            {{item.cookTimeText || '30分钟'}}
          </text>
          <text class="meta-item">
            <van-icon name="fire-o" size="12" color="#999" />
            {{item.difficultyText || '简单'}}
          </text>
          <text class="meta-item" wx:if="{{item.rating}}">
            <van-icon name="star" size="12" color="#FFD700" />
            {{item.rating}}
          </text>
        </view>
        <text class="favorite-time">{{item.addTimeText}}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="item-actions" wx:if="{{!editMode}}">
        <view class="action-btn" bindtap="shareRecipe" data-recipe="{{item}}">
          <van-icon name="share-o" size="16" color="#666" />
        </view>
        <view class="action-btn" bindtap="removeFavorite" data-id="{{item.id}}">
          <van-icon name="delete-o" size="16" color="#ff4444" />
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{displayList.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-favorites.png" mode="aspectFit" />
    <text class="empty-title">{{searchKeyword ? '没有找到相关菜谱' : '还没有收藏任何菜谱'}}</text>
    <text class="empty-desc">{{searchKeyword ? '试试其他关键词' : '去发现一些美味的菜谱吧'}}</text>
    <van-button 
      type="primary" 
      size="small" 
      bindtap="goDiscover"
      wx:if="{{!searchKeyword}}"
    >
      去发现
    </van-button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <van-loading size="24" color="#4CAF50">加载中...</van-loading>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{editMode && favoriteList.length > 0}}">
    <view class="select-all" bindtap="toggleSelectAll">
      <van-icon 
        name="{{allSelected ? 'checked' : 'circle'}}" 
        size="18" 
        color="{{allSelected ? '#4CAF50' : '#ccc'}}" 
      />
      <text>全选</text>
    </view>
    
    <view class="action-buttons">
      <van-button 
        type="default" 
        size="small" 
        bindtap="batchShare"
        disabled="{{selectedCount === 0}}"
      >
        分享 ({{selectedCount}})
      </van-button>
      <van-button 
        type="danger" 
        size="small" 
        bindtap="batchDelete"
        disabled="{{selectedCount === 0}}"
      >
        删除 ({{selectedCount}})
      </van-button>
    </view>
  </view>
</view>

<!-- 排序弹窗 -->
<van-popup 
  show="{{showSortModal}}" 
  position="bottom" 
  round
  bind:close="hideSortModal"
>
  <view class="sort-modal">
    <view class="modal-header">
      <text class="modal-title">排序方式</text>
      <van-icon name="cross" size="18" bindtap="hideSortModal" />
    </view>
    
    <view class="sort-options">
      <view 
        class="sort-option {{sortType === item.value ? 'active' : ''}}" 
        wx:for="{{sortOptions}}" 
        wx:key="value"
        bindtap="selectSort"
        data-type="{{item.value}}"
      >
        <text class="option-text">{{item.label}}</text>
        <van-icon 
          name="success" 
          size="16" 
          color="#4CAF50" 
          wx:if="{{sortType === item.value}}"
        />
      </view>
    </view>
  </view>
</van-popup>

<!-- 全局提示 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<van-notify id="van-notify" />
