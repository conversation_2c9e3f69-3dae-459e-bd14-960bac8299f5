/* 拍照识别页面样式 */

.container {
  width: 100%;
  height: 100vh;
  background: #000;
  position: relative;
  overflow: hidden;
}

/* 相机预览区域 */
.camera-section {
  width: 100%;
  height: 100%;
  position: relative;
}

.camera {
  width: 100%;
  height: 100%;
}

/* 相机控制层 */
.camera-controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.camera-controls > * {
  pointer-events: auto;
}

/* 顶部控制栏 */
.top-controls {
  position: absolute;
  top: 60rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 40rpx;
  z-index: 11;
}

.control-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10rpx);
}

.control-item text {
  color: white;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 底部拍照区域 */
.bottom-controls {
  position: absolute;
  bottom: 80rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 60rpx;
  z-index: 11;
}

.album-btn,
.help-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10rpx);
}

.album-btn text,
.help-btn text {
  color: white;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 拍照按钮 */
.capture-btn {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 6rpx solid white;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
}

.capture-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.capture-inner {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: white;
}

/* 拍照提示 */
.camera-tips {
  position: absolute;
  bottom: 280rpx;
  left: 50%;
  transform: translateX(-50%);
  padding: 20rpx 40rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 40rpx;
  backdrop-filter: blur(10rpx);
  z-index: 11;
}

.camera-tips text {
  color: white;
  font-size: 28rpx;
  text-align: center;
}

/* 图片预览区域 */
.preview-section {
  width: 100%;
  height: 100%;
  background: #000;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60rpx 40rpx 20rpx;
  background: rgba(0, 0, 0, 0.8);
  color: white;
}

.preview-header text {
  font-size: 32rpx;
  font-weight: 500;
}

.confirm-btn {
  color: #4CAF50 !important;
  font-weight: 600;
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.preview-image {
  max-width: 100%;
  max-height: 70%;
  border-radius: 12rpx;
}

/* 图片操作按钮 */
.image-actions {
  display: flex;
  justify-content: center;
  gap: 80rpx;
  margin-top: 60rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.action-btn text {
  color: white;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 识别中状态 */
.recognizing-section {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
}

.recognizing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.recognizing-text {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 30rpx;
}

.recognizing-tip {
  color: #666;
  font-size: 26rpx;
  margin-top: 20rpx;
  text-align: center;
}

/* 权限提示 */
.permission-section {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
}

.permission-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx;
  background: white;
  border-radius: 20rpx;
  margin: 0 40rpx;
}

.permission-title {
  color: #333;
  font-size: 36rpx;
  font-weight: 600;
  margin-top: 30rpx;
}

.permission-desc {
  color: #666;
  font-size: 28rpx;
  text-align: center;
  margin-top: 20rpx;
  line-height: 1.5;
}

.permission-actions {
  display: flex;
  gap: 30rpx;
  margin-top: 40rpx;
}

/* 帮助弹窗 */
.help-modal {
  padding: 40rpx;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
}

.help-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.help-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.help-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.help-item text {
  color: #666;
  font-size: 28rpx;
  line-height: 1.4;
}

/* 响应式适配 */
@media (max-height: 600px) {
  .camera-tips {
    bottom: 200rpx;
  }
  
  .bottom-controls {
    bottom: 40rpx;
  }
}

/* 动画效果 */
.camera-controls .control-item,
.album-btn,
.help-btn,
.action-btn {
  transition: all 0.2s ease;
}

.camera-controls .control-item:active,
.album-btn:active,
.help-btn:active,
.action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 加载动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recognizing-content,
.permission-content {
  animation: fadeIn 0.3s ease;
}
