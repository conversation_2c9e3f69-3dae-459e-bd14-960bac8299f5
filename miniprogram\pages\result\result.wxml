<!--识别结果页面-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <van-icon name="arrow-left" size="20" color="#333" />
        <text>返回</text>
      </view>
      <view class="navbar-title">识别结果</view>
      <view class="navbar-right" bindtap="showMenu">
        <van-icon name="ellipsis" size="20" color="#333" />
      </view>
    </view>
  </view>

  <!-- 识别图片展示 -->
  <view class="image-section">
    <image 
      class="result-image" 
      src="{{originalImage}}" 
      mode="aspectFill"
      bindtap="previewImage"
    />
    <view class="image-overlay">
      <view class="confidence-badge" wx:if="{{confidence}}">
        <van-icon name="star" size="12" color="#FFD700" />
        <text>识别准确度 {{confidence}}%</text>
      </view>
    </view>
  </view>

  <!-- 识别结果内容 -->
  <view class="content-section">
    <!-- 识别状态 -->
    <view class="status-section" wx:if="{{recognitionStatus}}">
      <view class="status-item success" wx:if="{{recognitionStatus === 'success'}}">
        <van-icon name="success" size="16" color="#4CAF50" />
        <text>识别成功</text>
      </view>
      <view class="status-item warning" wx:elif="{{recognitionStatus === 'partial'}}">
        <van-icon name="warning" size="16" color="#FF9800" />
        <text>部分识别</text>
      </view>
      <view class="status-item error" wx:elif="{{recognitionStatus === 'failed'}}">
        <van-icon name="cross" size="16" color="#F44336" />
        <text>识别失败</text>
      </view>
    </view>

    <!-- 识别到的食材列表 -->
    <view class="ingredients-section" wx:if="{{ingredients && ingredients.length > 0}}">
      <view class="section-header">
        <van-icon name="apps-o" size="18" color="#4CAF50" />
        <text class="section-title">识别到的食材</text>
        <text class="ingredient-count">({{ingredients.length}}种)</text>
      </view>
      
      <view class="ingredients-list">
        <view 
          class="ingredient-item {{item.selected ? 'selected' : ''}}" 
          wx:for="{{ingredients}}" 
          wx:key="id"
          bindtap="toggleIngredient"
          data-index="{{index}}"
        >
          <view class="ingredient-info">
            <view class="ingredient-name">{{item.name}}</view>
            <view class="ingredient-details">
              <text class="confidence">置信度: {{item.confidence}}%</text>
              <text class="category" wx:if="{{item.category}}">{{item.category}}</text>
            </view>
          </view>
          <view class="ingredient-actions">
            <view class="quantity-input" wx:if="{{item.selected}}">
              <van-stepper 
                value="{{item.quantity || 1}}" 
                min="0.1" 
                step="0.1" 
                decimal-length="1"
                bind:change="onQuantityChange"
                data-index="{{index}}"
              />
              <text class="unit">{{item.unit || '个'}}</text>
            </view>
            <van-checkbox 
              value="{{item.selected}}" 
              checked-color="#4CAF50"
              bind:change="toggleIngredient"
              data-index="{{index}}"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 营养信息预览 -->
    <view class="nutrition-section" wx:if="{{nutritionInfo}}">
      <view class="section-header">
        <van-icon name="fire-o" size="18" color="#FF5722" />
        <text class="section-title">营养信息</text>
      </view>
      
      <view class="nutrition-grid">
        <view class="nutrition-item" wx:for="{{nutritionInfo}}" wx:key="key">
          <view class="nutrition-icon">{{item.icon}}</view>
          <view class="nutrition-info">
            <text class="nutrition-name">{{item.name}}</text>
            <text class="nutrition-value">{{item.formatted}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐菜谱预览 -->
    <view class="recipes-preview" wx:if="{{recommendedRecipes && recommendedRecipes.length > 0}}">
      <view class="section-header">
        <van-icon name="bookmark-o" size="18" color="#2196F3" />
        <text class="section-title">推荐菜谱</text>
        <text class="recipe-count">({{recommendedRecipes.length}}道)</text>
      </view>
      
      <scroll-view class="recipes-scroll" scroll-x>
        <view class="recipes-list">
          <view 
            class="recipe-card" 
            wx:for="{{recommendedRecipes}}" 
            wx:key="id"
            bindtap="viewRecipe"
            data-recipe="{{item}}"
          >
            <image class="recipe-image" src="{{item.image || '/images/default-recipe.png'}}" mode="aspectFill" />
            <view class="recipe-info">
              <text class="recipe-name">{{item.name}}</text>
              <view class="recipe-meta">
                <text class="cook-time">{{item.cookTime}}分钟</text>
                <text class="difficulty">{{item.difficulty}}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="actions-section">
      <view class="action-buttons">
        <van-button 
          type="default" 
          size="large" 
          icon="photograph"
          bindtap="retakePhoto"
        >
          重新拍照
        </van-button>
        
        <van-button 
          type="primary" 
          size="large" 
          icon="arrow"
          bindtap="generateRecipes"
          disabled="{{!hasSelectedIngredients}}"
          loading="{{generating}}"
        >
          {{generating ? '生成中...' : '生成菜谱'}}
        </van-button>
      </view>
      
      <!-- 额外操作 -->
      <view class="extra-actions">
        <view class="action-item" bindtap="saveResult">
          <van-icon name="star-o" size="20" color="#666" />
          <text>保存结果</text>
        </view>
        <view class="action-item" bindtap="shareResult">
          <van-icon name="share-o" size="20" color="#666" />
          <text>分享</text>
        </view>
        <view class="action-item" bindtap="addToShoppingList">
          <van-icon name="shopping-cart-o" size="20" color="#666" />
          <text>购物清单</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!ingredients || ingredients.length === 0}}">
    <van-empty 
      image="search" 
      description="未识别到食材"
    >
      <van-button 
        type="primary" 
        size="small" 
        bindtap="retakePhoto"
      >
        重新拍照
      </van-button>
    </van-empty>
  </view>
</view>

<!-- 菜单弹窗 -->
<van-action-sheet
  show="{{showMenuModal}}"
  actions="{{menuActions}}"
  bind:close="hideMenu"
  bind:select="onMenuSelect"
/>

<!-- 食材详情弹窗 -->
<van-popup 
  show="{{showIngredientDetail}}" 
  position="bottom" 
  round
  bind:close="hideIngredientDetail"
>
  <view class="ingredient-detail-modal" wx:if="{{selectedIngredient}}">
    <view class="modal-header">
      <text class="modal-title">{{selectedIngredient.name}}</text>
      <van-icon name="cross" size="18" bindtap="hideIngredientDetail" />
    </view>
    
    <view class="modal-content">
      <view class="detail-item">
        <text class="label">分类:</text>
        <text class="value">{{selectedIngredient.category || '未知'}}</text>
      </view>
      <view class="detail-item">
        <text class="label">置信度:</text>
        <text class="value">{{selectedIngredient.confidence}}%</text>
      </view>
      <view class="detail-item" wx:if="{{selectedIngredient.nutrition}}">
        <text class="label">营养信息:</text>
        <view class="nutrition-list">
          <text class="nutrition-item" wx:for="{{selectedIngredient.nutrition}}" wx:key="key">
            {{item.name}}: {{item.value}}{{item.unit}}
          </text>
        </view>
      </view>
    </view>
  </view>
</van-popup>

<!-- 全局提示 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<van-notify id="van-notify" />
