// 本地存储工具类

/**
 * 存储工具类
 */
class StorageUtils {
  
  /**
   * 设置存储数据
   * @param {string} key 存储键
   * @param {any} value 存储值
   * @param {number} expire 过期时间(秒)，0表示永不过期
   * @returns {boolean} 是否成功
   */
  set(key, value, expire = 0) {
    try {
      const data = {
        value,
        timestamp: Date.now(),
        expire: expire > 0 ? Date.now() + expire * 1000 : 0
      };
      
      wx.setStorageSync(key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Storage set error:', error);
      return false;
    }
  }

  /**
   * 获取存储数据
   * @param {string} key 存储键
   * @param {any} defaultValue 默认值
   * @returns {any} 存储值
   */
  get(key, defaultValue = null) {
    try {
      const dataStr = wx.getStorageSync(key);
      if (!dataStr) {
        return defaultValue;
      }

      const data = JSON.parse(dataStr);
      
      // 检查是否过期
      if (data.expire > 0 && Date.now() > data.expire) {
        this.remove(key);
        return defaultValue;
      }

      return data.value;
    } catch (error) {
      console.error('Storage get error:', error);
      return defaultValue;
    }
  }

  /**
   * 移除存储数据
   * @param {string} key 存储键
   * @returns {boolean} 是否成功
   */
  remove(key) {
    try {
      wx.removeStorageSync(key);
      return true;
    } catch (error) {
      console.error('Storage remove error:', error);
      return false;
    }
  }

  /**
   * 清空所有存储
   * @returns {boolean} 是否成功
   */
  clear() {
    try {
      wx.clearStorageSync();
      return true;
    } catch (error) {
      console.error('Storage clear error:', error);
      return false;
    }
  }

  /**
   * 获取所有存储键
   * @returns {Array<string>} 存储键数组
   */
  keys() {
    try {
      const info = wx.getStorageInfoSync();
      return info.keys || [];
    } catch (error) {
      console.error('Storage keys error:', error);
      return [];
    }
  }

  /**
   * 获取存储信息
   * @returns {Object} 存储信息
   */
  info() {
    try {
      return wx.getStorageInfoSync();
    } catch (error) {
      console.error('Storage info error:', error);
      return { keys: [], currentSize: 0, limitSize: 0 };
    }
  }

  /**
   * 检查存储是否存在
   * @param {string} key 存储键
   * @returns {boolean} 是否存在
   */
  has(key) {
    try {
      const dataStr = wx.getStorageSync(key);
      if (!dataStr) {
        return false;
      }

      const data = JSON.parse(dataStr);
      
      // 检查是否过期
      if (data.expire > 0 && Date.now() > data.expire) {
        this.remove(key);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Storage has error:', error);
      return false;
    }
  }

  /**
   * 批量设置存储
   * @param {Object} data 数据对象
   * @param {number} expire 过期时间(秒)
   * @returns {boolean} 是否成功
   */
  setMultiple(data, expire = 0) {
    try {
      Object.keys(data).forEach(key => {
        this.set(key, data[key], expire);
      });
      return true;
    } catch (error) {
      console.error('Storage setMultiple error:', error);
      return false;
    }
  }

  /**
   * 批量获取存储
   * @param {Array<string>} keys 存储键数组
   * @returns {Object} 存储数据对象
   */
  getMultiple(keys) {
    const result = {};
    keys.forEach(key => {
      result[key] = this.get(key);
    });
    return result;
  }

  /**
   * 批量移除存储
   * @param {Array<string>} keys 存储键数组
   * @returns {boolean} 是否成功
   */
  removeMultiple(keys) {
    try {
      keys.forEach(key => {
        this.remove(key);
      });
      return true;
    } catch (error) {
      console.error('Storage removeMultiple error:', error);
      return false;
    }
  }

  /**
   * 清理过期数据
   * @returns {number} 清理的数据条数
   */
  clearExpired() {
    let count = 0;
    try {
      const keys = this.keys();
      keys.forEach(key => {
        try {
          const dataStr = wx.getStorageSync(key);
          if (dataStr) {
            const data = JSON.parse(dataStr);
            if (data.expire > 0 && Date.now() > data.expire) {
              this.remove(key);
              count++;
            }
          }
        } catch (e) {
          // 忽略解析错误的数据
        }
      });
    } catch (error) {
      console.error('Storage clearExpired error:', error);
    }
    return count;
  }

  /**
   * 获取存储大小（KB）
   * @returns {number} 存储大小
   */
  getSize() {
    try {
      const info = this.info();
      return Math.round(info.currentSize || 0);
    } catch (error) {
      console.error('Storage getSize error:', error);
      return 0;
    }
  }

  /**
   * 检查存储是否快满了
   * @param {number} threshold 阈值(0-1)
   * @returns {boolean} 是否快满了
   */
  isAlmostFull(threshold = 0.8) {
    try {
      const info = this.info();
      const usage = info.currentSize / info.limitSize;
      return usage >= threshold;
    } catch (error) {
      console.error('Storage isAlmostFull error:', error);
      return false;
    }
  }
}

// 创建实例
const storage = new StorageUtils();

// 用户相关存储键
export const USER_KEYS = {
  TOKEN: 'user_token',
  USER_INFO: 'user_info',
  USER_PREFERENCES: 'user_preferences'
};

// 应用相关存储键
export const APP_KEYS = {
  CACHE_VERSION: 'cache_version',
  LAST_UPDATE: 'last_update',
  SETTINGS: 'app_settings'
};

// 缓存相关存储键
export const CACHE_KEYS = {
  RECOGNITION_HISTORY: 'recognition_history',
  RECIPE_CACHE: 'recipe_cache',
  IMAGE_CACHE: 'image_cache'
};

// 导出常用方法
export const setStorage = storage.set.bind(storage);
export const getStorage = storage.get.bind(storage);
export const removeStorage = storage.remove.bind(storage);
export const clearStorage = storage.clear.bind(storage);
export const hasStorage = storage.has.bind(storage);
export const getStorageInfo = storage.info.bind(storage);
export const clearExpiredStorage = storage.clearExpired.bind(storage);

export default storage;
