// 拍照识别页面逻辑
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { upload } from '../../utils/api';
import { smartCompress, getImageSize } from '../../utils/image';

Page({
  data: {
    // 相机状态
    cameraPosition: 'back', // 'front' | 'back'
    flashMode: 'off', // 'auto' | 'on' | 'off'
    cameraReady: false,
    
    // 图片状态
    selectedImage: '', // 选中的图片路径
    imageWidth: 0,
    imageHeight: 0,
    
    // 识别状态
    recognizing: false,
    recognizingTip: '正在分析图片中的食材...',
    
    // UI状态
    showPermissionTip: false,
    showHelpModal: false,
    
    // 识别提示文案
    recognizingTips: [
      '正在分析图片中的食材...',
      'AI正在识别食材种类...',
      '正在分析营养成分...',
      '即将为您生成菜谱...'
    ]
  },

  onLoad(options) {
    console.log('Camera page loaded with options:', options);
    this.checkCameraPermission();
  },

  onShow() {
    // 页面显示时重置状态
    this.setData({
      selectedImage: '',
      recognizing: false,
      showPermissionTip: false
    });
  },

  onHide() {
    // 页面隐藏时停止识别动画
    if (this.recognizingTimer) {
      clearInterval(this.recognizingTimer);
      this.recognizingTimer = null;
    }
  },

  onUnload() {
    // 页面卸载时清理资源
    if (this.recognizingTimer) {
      clearInterval(this.recognizingTimer);
      this.recognizingTimer = null;
    }
  },

  // 检查相机权限
  checkCameraPermission() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.camera'] === false) {
          this.setData({ showPermissionTip: true });
        }
      }
    });
  },

  // 相机事件处理
  onCameraReady() {
    console.log('Camera ready');
    this.setData({ cameraReady: true });
  },

  onCameraError(e) {
    console.error('Camera error:', e.detail);
    Toast.fail('相机启动失败');
    
    // 检查是否是权限问题
    if (e.detail.errMsg.includes('authorize')) {
      this.setData({ showPermissionTip: true });
    }
  },

  onCameraStop() {
    console.log('Camera stopped');
    this.setData({ cameraReady: false });
  },

  // 拍照功能
  takePhoto() {
    if (!this.data.cameraReady) {
      Toast.fail('相机未准备就绪');
      return;
    }

    const ctx = wx.createCameraContext();
    ctx.takePhoto({
      quality: 'high',
      success: (res) => {
        console.log('Photo taken:', res.tempImagePath);
        this.setData({
          selectedImage: res.tempImagePath
        });
        this.analyzeImageSize(res.tempImagePath);
      },
      fail: (err) => {
        console.error('Take photo failed:', err);
        Toast.fail('拍照失败，请重试');
      }
    });
  },

  // 从相册选择图片
  async chooseFromAlbum() {
    try {
      const res = await new Promise((resolve, reject) => {
        wx.chooseImage({
          count: 1,
          sizeType: ['original', 'compressed'],
          sourceType: ['album'],
          success: resolve,
          fail: reject
        });
      });

      const imagePath = res.tempFilePaths[0];
      console.log('Image chosen from album:', imagePath);

      // 检查图片大小，如果太大则压缩
      const imageSize = await getImageSize(imagePath);
      console.log('Original image size:', imageSize, 'KB');

      let finalImagePath = imagePath;
      if (imageSize > 1000) { // 大于1MB时压缩
        Toast.loading('处理图片中...');
        finalImagePath = await smartCompress(imagePath, 800);
        Toast.clear();
        console.log('Image compressed, new size:', await getImageSize(finalImagePath), 'KB');
      }

      this.setData({
        selectedImage: finalImagePath
      });
      this.analyzeImageSize(finalImagePath);

    } catch (err) {
      console.error('Choose image failed:', err);
      if (err.errMsg && err.errMsg.includes('cancel')) {
        return; // 用户取消选择
      }
      Toast.fail('选择图片失败');
    }
  },

  // 分析图片尺寸
  analyzeImageSize(imagePath) {
    wx.getImageInfo({
      src: imagePath,
      success: (res) => {
        this.setData({
          imageWidth: res.width,
          imageHeight: res.height
        });
        console.log('Image info:', res);
      },
      fail: (err) => {
        console.error('Get image info failed:', err);
      }
    });
  },

  // 图片加载完成
  onImageLoad(e) {
    console.log('Image loaded:', e.detail);
  },

  // 确认图片并开始识别
  confirmImage() {
    if (!this.data.selectedImage) {
      Toast.fail('请先选择图片');
      return;
    }

    this.startRecognition();
  },

  // 开始识别流程
  startRecognition() {
    this.setData({ recognizing: true });
    this.startRecognizingAnimation();

    // 模拟识别过程（实际应该调用后端API）
    this.performRecognition()
      .then((result) => {
        console.log('Recognition result:', result);
        this.handleRecognitionSuccess(result);
      })
      .catch((error) => {
        console.error('Recognition failed:', error);
        this.handleRecognitionError(error);
      });
  },

  // 执行识别（调用后端API）
  async performRecognition() {
    try {
      // 先压缩图片
      const compressedImage = await smartCompress(this.data.selectedImage, 500);
      console.log('Image compressed for recognition');

      // 上传图片到后端进行识别
      const result = await upload('/api/ai/recognize-image', compressedImage, {
        name: 'image',
        showLoading: false, // 我们已经有自己的loading
        loadingText: '识别中...'
      });

      return result.data;
    } catch (error) {
      console.error('Recognition failed:', error);
      throw error;
    }
  },

  // 识别成功处理
  handleRecognitionSuccess(result) {
    this.stopRecognizingAnimation();
    this.setData({ recognizing: false });

    // 跳转到结果页面
    wx.navigateTo({
      url: `/pages/result/result?data=${encodeURIComponent(JSON.stringify(result))}`
    });
  },

  // 识别失败处理
  handleRecognitionError(error) {
    this.stopRecognizingAnimation();
    this.setData({ recognizing: false });
    
    Toast.fail(error.message || '识别失败，请重试');
  },

  // 开始识别动画
  startRecognizingAnimation() {
    let tipIndex = 0;
    this.recognizingTimer = setInterval(() => {
      tipIndex = (tipIndex + 1) % this.data.recognizingTips.length;
      this.setData({
        recognizingTip: this.data.recognizingTips[tipIndex]
      });
    }, 2000);
  },

  // 停止识别动画
  stopRecognizingAnimation() {
    if (this.recognizingTimer) {
      clearInterval(this.recognizingTimer);
      this.recognizingTimer = null;
    }
  },

  // 相机控制
  toggleFlash() {
    const flashModes = ['off', 'auto', 'on'];
    const currentIndex = flashModes.indexOf(this.data.flashMode);
    const nextIndex = (currentIndex + 1) % flashModes.length;
    
    this.setData({
      flashMode: flashModes[nextIndex]
    });
    
    Toast(`闪光灯：${flashModes[nextIndex] === 'off' ? '关闭' : flashModes[nextIndex] === 'auto' ? '自动' : '开启'}`);
  },

  switchCamera() {
    this.setData({
      cameraPosition: this.data.cameraPosition === 'back' ? 'front' : 'back'
    });
    
    Toast(`切换到${this.data.cameraPosition === 'back' ? '后' : '前'}置摄像头`);
  },

  // 图片操作
  retakePhoto() {
    this.setData({
      selectedImage: '',
      imageWidth: 0,
      imageHeight: 0
    });
  },

  backToCamera() {
    this.retakePhoto();
  },

  // 帮助功能
  showHelp() {
    this.setData({ showHelpModal: true });
  },

  hideHelp() {
    this.setData({ showHelpModal: false });
  },

  // 权限处理
  hidePermissionTip() {
    this.setData({ showPermissionTip: false });
  },

  openSettings() {
    wx.openSetting({
      success: (res) => {
        if (res.authSetting['scope.camera']) {
          this.setData({ showPermissionTip: false });
          Toast.success('权限已开启');
        }
      }
    });
  }
});
