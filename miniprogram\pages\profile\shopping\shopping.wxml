<!--购物清单页面-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="goBack">
        <van-icon name="arrow-left" size="20" color="#333" />
      </view>
      <view class="navbar-title">购物清单</view>
      <view class="navbar-right" bindtap="toggleEditMode">
        <text class="edit-btn">{{editMode ? '完成' : '编辑'}}</text>
      </view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-bar">
    <view class="stats-left">
      <text class="stats-text">共 {{shoppingList.length}} 项</text>
      <text class="checked-text">已购买 {{checkedCount}} 项</text>
    </view>
    <view class="stats-actions" wx:if="{{shoppingList.length > 0}}">
      <text class="action-btn" bindtap="addItem">
        <van-icon name="plus" size="14" />
        添加
      </text>
      <text class="action-btn" bindtap="clearChecked">
        <van-icon name="delete-o" size="14" />
        清理
      </text>
    </view>
  </view>

  <!-- 快速添加栏 -->
  <view class="quick-add" wx:if="{{!editMode}}">
    <van-field
      value="{{newItemName}}"
      placeholder="输入食材名称，快速添加到清单"
      bind:change="onNewItemChange"
      bind:confirm="addNewItem"
      confirm-type="done"
      use-button-slot
    >
      <van-button 
        slot="button" 
        size="small" 
        type="primary"
        bindtap="addNewItem"
        disabled="{{!newItemName.trim()}}"
      >
        添加
      </van-button>
    </van-field>
  </view>

  <!-- 购物清单列表 -->
  <view class="shopping-list" wx:if="{{displayList.length > 0}}">
    <!-- 按分类分组显示 -->
    <view 
      class="category-group" 
      wx:for="{{groupedList}}" 
      wx:key="category"
    >
      <view class="category-header">
        <view class="category-info">
          <van-icon name="{{item.icon}}" size="16" color="{{item.color}}" />
          <text class="category-name">{{item.categoryName}}</text>
          <text class="item-count">({{item.items.length}})</text>
        </view>
        <view class="category-actions" wx:if="{{editMode}}">
          <text class="action-text" bindtap="selectCategory" data-category="{{item.category}}">
            {{item.allSelected ? '取消全选' : '全选'}}
          </text>
        </view>
      </view>
      
      <view 
        class="shopping-item {{editMode ? 'edit-mode' : ''}} {{item.checked ? 'checked' : ''}}"
        wx:for="{{item.items}}"
        wx:key="id"
        wx:for-item="shopItem"
        bindtap="toggleCheck"
        data-id="{{shopItem.id}}"
      >
        <!-- 选择框 -->
        <view class="item-checkbox" bindtap="toggleCheck" data-id="{{shopItem.id}}">
          <van-icon 
            name="{{shopItem.checked ? 'checked' : 'circle'}}" 
            size="20" 
            color="{{shopItem.checked ? '#4CAF50' : '#ccc'}}" 
          />
        </view>

        <!-- 食材信息 -->
        <view class="item-info">
          <text class="item-name {{shopItem.checked ? 'checked-text' : ''}}">{{shopItem.name}}</text>
          <view class="item-meta" wx:if="{{shopItem.quantity || shopItem.unit || shopItem.note}}">
            <text class="meta-item" wx:if="{{shopItem.quantity}}">{{shopItem.quantity}}</text>
            <text class="meta-item" wx:if="{{shopItem.unit}}">{{shopItem.unit}}</text>
            <text class="meta-item" wx:if="{{shopItem.note}}">{{shopItem.note}}</text>
          </view>
          <text class="add-time" wx:if="{{shopItem.addTimeText}}">{{shopItem.addTimeText}}</text>
        </view>

        <!-- 编辑按钮 -->
        <view class="item-actions" wx:if="{{editMode}}">
          <view class="action-btn" bindtap="editItem" data-item="{{shopItem}}">
            <van-icon name="edit" size="16" color="#666" />
          </view>
          <view class="action-btn" bindtap="deleteItem" data-id="{{shopItem.id}}">
            <van-icon name="delete-o" size="16" color="#ff4444" />
          </view>
        </view>

        <!-- 拖拽手柄 -->
        <view class="drag-handle" wx:if="{{editMode}}">
          <van-icon name="bars" size="16" color="#ccc" />
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{displayList.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-shopping.png" mode="aspectFit" />
    <text class="empty-title">购物清单是空的</text>
    <text class="empty-desc">添加一些食材，开始你的购物之旅吧</text>
    <van-button 
      type="primary" 
      size="small" 
      bindtap="addFromRecipe"
    >
      从菜谱添加
    </van-button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <van-loading size="24" color="#4CAF50">加载中...</van-loading>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{shoppingList.length > 0}}">
    <view class="select-all" bindtap="toggleSelectAll" wx:if="{{editMode}}">
      <van-icon 
        name="{{allSelected ? 'checked' : 'circle'}}" 
        size="18" 
        color="{{allSelected ? '#4CAF50' : '#ccc'}}" 
      />
      <text>全选</text>
    </view>
    
    <view class="action-buttons">
      <van-button 
        type="default" 
        size="small" 
        bindtap="shareList"
        wx:if="{{!editMode}}"
      >
        分享清单
      </van-button>
      <van-button 
        type="primary" 
        size="small" 
        bindtap="goShopping"
        wx:if="{{!editMode && uncheckedCount > 0}}"
      >
        去购买 ({{uncheckedCount}})
      </van-button>
      <van-button 
        type="danger" 
        size="small" 
        bindtap="batchDelete"
        disabled="{{selectedCount === 0}}"
        wx:if="{{editMode}}"
      >
        删除 ({{selectedCount}})
      </van-button>
    </view>
  </view>
</view>

<!-- 添加/编辑弹窗 -->
<van-popup 
  show="{{showEditModal}}" 
  position="bottom" 
  round
  bind:close="hideEditModal"
>
  <view class="edit-modal">
    <view class="modal-header">
      <text class="modal-title">{{editingItem ? '编辑食材' : '添加食材'}}</text>
      <van-icon name="cross" size="18" bindtap="hideEditModal" />
    </view>
    
    <view class="edit-form">
      <van-field
        label="食材名称"
        value="{{editForm.name}}"
        placeholder="请输入食材名称"
        bind:change="onFormChange"
        data-field="name"
        required
      />
      <van-field
        label="数量"
        value="{{editForm.quantity}}"
        placeholder="请输入数量"
        bind:change="onFormChange"
        data-field="quantity"
        type="number"
      />
      <van-field
        label="单位"
        value="{{editForm.unit}}"
        placeholder="如：个、斤、包"
        bind:change="onFormChange"
        data-field="unit"
      />
      <van-field
        label="分类"
        value="{{editForm.categoryText}}"
        placeholder="选择分类"
        readonly
        is-link
        bindtap="showCategoryPicker"
      />
      <van-field
        label="备注"
        value="{{editForm.note}}"
        placeholder="可选备注信息"
        bind:change="onFormChange"
        data-field="note"
        type="textarea"
        autosize
      />
    </view>
    
    <view class="edit-actions">
      <van-button type="default" size="large" bindtap="hideEditModal">取消</van-button>
      <van-button type="primary" size="large" bindtap="saveItem">保存</van-button>
    </view>
  </view>
</van-popup>

<!-- 分类选择器 -->
<van-popup 
  show="{{showCategoryPicker}}" 
  position="bottom" 
  round
  bind:close="hideCategoryPicker"
>
  <view class="category-picker">
    <view class="picker-header">
      <text class="picker-title">选择分类</text>
      <van-icon name="cross" size="18" bindtap="hideCategoryPicker" />
    </view>
    
    <view class="category-options">
      <view 
        class="category-option {{editForm.category === item.value ? 'active' : ''}}"
        wx:for="{{categoryOptions}}"
        wx:key="value"
        bindtap="selectCategory"
        data-category="{{item.value}}"
      >
        <van-icon name="{{item.icon}}" size="20" color="{{item.color}}" />
        <text class="option-text">{{item.label}}</text>
        <van-icon 
          name="success" 
          size="16" 
          color="#4CAF50" 
          wx:if="{{editForm.category === item.value}}"
        />
      </view>
    </view>
  </view>
</van-popup>

<!-- 从菜谱添加弹窗 -->
<van-popup 
  show="{{showRecipeModal}}" 
  position="bottom" 
  round
  bind:close="hideRecipeModal"
>
  <view class="recipe-modal">
    <view class="modal-header">
      <text class="modal-title">从菜谱添加</text>
      <van-icon name="cross" size="18" bindtap="hideRecipeModal" />
    </view>
    
    <view class="recipe-list">
      <view 
        class="recipe-item"
        wx:for="{{recentRecipes}}"
        wx:key="id"
        bindtap="addFromRecipeItem"
        data-recipe="{{item}}"
      >
        <image class="recipe-thumb" src="{{item.image}}" mode="aspectFill" />
        <view class="recipe-info">
          <text class="recipe-name">{{item.name}}</text>
          <text class="ingredient-count">{{item.ingredients.length}} 种食材</text>
        </view>
        <van-icon name="arrow" size="16" color="#ccc" />
      </view>
    </view>
  </view>
</van-popup>

<!-- 全局提示 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<van-notify id="van-notify" />
