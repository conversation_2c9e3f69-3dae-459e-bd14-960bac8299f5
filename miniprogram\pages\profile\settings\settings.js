// 设置页面逻辑
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import Notify from '@vant/weapp/notify/notify';
import { get, post, put } from '../../../utils/api';
import { getStorage, setStorage, removeStorage, CACHE_KEYS } from '../../../utils/storage';

Page({
  data: {
    // 设置数据
    settings: {
      notifications: true,
      sound: true,
      autoSaveImages: false,
      theme: 'auto',
      language: 'zh-CN'
    },
    
    // 缓存信息
    cacheSize: '计算中...',
    imageCache: '0 MB',
    dataCache: '0 MB',
    totalCache: '0 MB',
    
    // 应用信息
    appVersion: '1.0.0',
    currentLanguage: '简体中文',
    currentTheme: '跟随系统',
    
    // 弹窗状态
    showCacheModal: false,
    showThemeModal: false,
    showLanguageModal: false,
    
    // 选项数据
    themeOptions: [
      { label: '跟随系统', value: 'auto', color: 'linear-gradient(45deg, #667eea, #764ba2)' },
      { label: '浅色模式', value: 'light', color: '#ffffff' },
      { label: '深色模式', value: 'dark', color: '#333333' },
      { label: '护眼模式', value: 'green', color: '#4CAF50' }
    ],
    
    languageOptions: [
      { label: '简体中文', value: 'zh-CN' },
      { label: '繁體中文', value: 'zh-TW' },
      { label: 'English', value: 'en-US' },
      { label: '日本語', value: 'ja-JP' }
    ]
  },

  onLoad() {
    console.log('Settings page loaded');
    this.loadSettings();
    this.calculateCacheSize();
  },

  onShow() {
    // 每次显示时刷新设置
    this.loadSettings();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 加载设置
  loadSettings() {
    try {
      // 从本地存储获取设置
      const settings = getStorage(CACHE_KEYS.USER_SETTINGS, {
        notifications: true,
        sound: true,
        autoSaveImages: false,
        theme: 'auto',
        language: 'zh-CN'
      });
      
      // 更新显示文本
      const currentLanguage = this.data.languageOptions.find(opt => opt.value === settings.language)?.label || '简体中文';
      const currentTheme = this.data.themeOptions.find(opt => opt.value === settings.theme)?.label || '跟随系统';
      
      this.setData({ 
        settings,
        currentLanguage,
        currentTheme
      });
    } catch (error) {
      console.error('Load settings failed:', error);
    }
  },

  // 保存设置
  saveSettings() {
    try {
      setStorage(CACHE_KEYS.USER_SETTINGS, this.data.settings);
      
      // 同步到服务器
      this.syncSettingsToServer();
    } catch (error) {
      console.error('Save settings failed:', error);
    }
  },

  // 同步设置到服务器
  async syncSettingsToServer() {
    try {
      await put('/api/users/settings', this.data.settings);
    } catch (error) {
      console.error('Sync settings failed:', error);
    }
  },

  // 切换通知设置
  toggleNotifications(e) {
    const notifications = e.detail;
    this.setData({
      'settings.notifications': notifications
    });
    this.saveSettings();
    
    if (notifications) {
      // 请求通知权限
      wx.requestSubscribeMessage({
        tmplIds: ['template_id_1', 'template_id_2'],
        success: (res) => {
          console.log('Subscribe message success:', res);
        },
        fail: (err) => {
          console.error('Subscribe message failed:', err);
        }
      });
    }
  },

  // 切换声音设置
  toggleSound(e) {
    const sound = e.detail;
    this.setData({
      'settings.sound': sound
    });
    this.saveSettings();
    
    if (sound) {
      Toast.success('声音提示已开启');
    } else {
      Toast.success('声音提示已关闭');
    }
  },

  // 切换自动保存图片
  toggleAutoSaveImages(e) {
    const autoSaveImages = e.detail;
    this.setData({
      'settings.autoSaveImages': autoSaveImages
    });
    this.saveSettings();
    
    if (autoSaveImages) {
      // 请求保存图片权限
      wx.authorize({
        scope: 'scope.writePhotosAlbum',
        success: () => {
          Toast.success('自动保存图片已开启');
        },
        fail: () => {
          Toast.fail('需要授权保存图片权限');
        }
      });
    } else {
      Toast.success('自动保存图片已关闭');
    }
  },

  // 计算缓存大小
  async calculateCacheSize() {
    try {
      // 这里应该计算实际的缓存大小
      // 由于小程序限制，这里使用模拟数据
      const imageCache = '12.5 MB';
      const dataCache = '3.2 MB';
      const totalCache = '15.7 MB';
      
      this.setData({
        cacheSize: totalCache,
        imageCache,
        dataCache,
        totalCache
      });
    } catch (error) {
      console.error('Calculate cache size failed:', error);
      this.setData({
        cacheSize: '计算失败'
      });
    }
  },

  // 编辑个人信息
  editProfile() {
    wx.navigateTo({
      url: '/pages/profile/edit/edit'
    });
  },

  // 管理偏好设置
  managePreferences() {
    wx.navigateTo({
      url: '/pages/profile/preferences/preferences'
    });
  },

  // 隐私设置
  privacySettings() {
    wx.navigateTo({
      url: '/pages/profile/privacy/privacy'
    });
  },

  // 显示缓存管理弹窗
  cacheSettings() {
    this.setData({ showCacheModal: true });
    this.calculateCacheSize();
  },

  // 隐藏缓存管理弹窗
  hideCacheModal() {
    this.setData({ showCacheModal: false });
  },

  // 清理图片缓存
  async clearImageCache() {
    try {
      const result = await Dialog.confirm({
        title: '确认清理',
        message: '确定要清理图片缓存吗？这将删除所有缓存的图片。'
      });
      
      if (result) {
        // 清理图片缓存的逻辑
        // wx.clearStorage() 或其他清理方法
        
        this.setData({
          imageCache: '0 MB'
        });
        this.calculateCacheSize();
        Toast.success('图片缓存已清理');
      }
    } catch (error) {
      // 用户取消
    }
  },

  // 清理全部缓存
  async clearAllCache() {
    try {
      const result = await Dialog.confirm({
        title: '确认清理',
        message: '确定要清理全部缓存吗？这将删除所有缓存数据，可能需要重新加载内容。'
      });
      
      if (result) {
        // 清理全部缓存，但保留用户设置
        const settings = this.data.settings;
        wx.clearStorageSync();
        setStorage(CACHE_KEYS.USER_SETTINGS, settings);
        
        this.setData({
          imageCache: '0 MB',
          dataCache: '0 MB',
          totalCache: '0 MB',
          cacheSize: '0 MB'
        });
        
        Toast.success('缓存已清理');
        this.setData({ showCacheModal: false });
      }
    } catch (error) {
      // 用户取消
    }
  },

  // AI设置
  aiSettings() {
    wx.navigateTo({
      url: '/pages/profile/ai-settings/ai-settings'
    });
  },

  // 显示语言设置
  languageSettings() {
    this.setData({ showLanguageModal: true });
  },

  // 隐藏语言设置
  hideLanguageModal() {
    this.setData({ showLanguageModal: false });
  },

  // 选择语言
  selectLanguage(e) {
    const language = e.currentTarget.dataset.language;
    const languageInfo = this.data.languageOptions.find(opt => opt.value === language);
    
    this.setData({
      'settings.language': language,
      currentLanguage: languageInfo.label,
      showLanguageModal: false
    });
    
    this.saveSettings();
    Toast.success(`已切换到${languageInfo.label}`);
  },

  // 显示主题设置
  themeSettings() {
    this.setData({ showThemeModal: true });
  },

  // 隐藏主题设置
  hideThemeModal() {
    this.setData({ showThemeModal: false });
  },

  // 选择主题
  selectTheme(e) {
    const theme = e.currentTarget.dataset.theme;
    const themeInfo = this.data.themeOptions.find(opt => opt.value === theme);
    
    this.setData({
      'settings.theme': theme,
      currentTheme: themeInfo.label,
      showThemeModal: false
    });
    
    this.saveSettings();
    Toast.success(`已切换到${themeInfo.label}`);
    
    // 应用主题
    this.applyTheme(theme);
  },

  // 应用主题
  applyTheme(theme) {
    // 这里可以实现主题切换逻辑
    console.log('Apply theme:', theme);
  },

  // 帮助中心
  helpCenter() {
    wx.navigateTo({
      url: '/pages/help/help'
    });
  },

  // 意见反馈
  feedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  // 联系我们
  contactUs() {
    wx.showModal({
      title: '联系我们',
      content: '客服微信：airecipe_service\n客服邮箱：<EMAIL>\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 检查更新
  checkUpdate() {
    wx.showLoading({
      title: '检查中...'
    });
    
    // 检查小程序更新
    const updateManager = wx.getUpdateManager();
    
    updateManager.onCheckForUpdate((res) => {
      wx.hideLoading();
      if (res.hasUpdate) {
        wx.showModal({
          title: '发现新版本',
          content: '发现新版本，是否立即更新？',
          success: (res) => {
            if (res.confirm) {
              wx.showLoading({
                title: '更新中...'
              });
            }
          }
        });
      } else {
        Toast.success('已是最新版本');
      }
    });
    
    updateManager.onUpdateReady(() => {
      wx.hideLoading();
      wx.showModal({
        title: '更新完成',
        content: '新版本已下载完成，是否立即重启应用？',
        success: (res) => {
          if (res.confirm) {
            updateManager.applyUpdate();
          }
        }
      });
    });
    
    updateManager.onUpdateFailed(() => {
      wx.hideLoading();
      Toast.fail('更新失败，请稍后重试');
    });
  },

  // 关于应用
  aboutApp() {
    wx.navigateTo({
      url: '/pages/profile/about/about'
    });
  },

  // 隐私政策
  privacyPolicy() {
    wx.navigateTo({
      url: '/pages/legal/privacy/privacy'
    });
  },

  // 用户协议
  userAgreement() {
    wx.navigateTo({
      url: '/pages/legal/agreement/agreement'
    });
  },

  // 退出登录
  async logout() {
    try {
      const result = await Dialog.confirm({
        title: '确认退出',
        message: '确定要退出登录吗？退出后将无法使用个人功能。'
      });
      
      if (result) {
        // 清除用户相关数据
        removeStorage(CACHE_KEYS.USER_INFO);
        removeStorage(CACHE_KEYS.USER_TOKEN);
        removeStorage(CACHE_KEYS.USER_FAVORITES);
        removeStorage(CACHE_KEYS.USER_HISTORY);
        
        // 通知服务器退出登录
        try {
          await post('/api/auth/logout');
        } catch (error) {
          console.error('Logout API failed:', error);
        }
        
        Toast.success('已退出登录');
        
        // 返回首页
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }
    } catch (error) {
      // 用户取消
    }
  }
});
