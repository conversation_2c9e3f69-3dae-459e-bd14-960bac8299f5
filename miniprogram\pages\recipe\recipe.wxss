/* 菜谱列表页面样式 */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.navbar-left text {
  font-size: 32rpx;
  color: #333;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 60rpx;
  display: flex;
  justify-content: flex-end;
}

/* 食材信息头部 */
.ingredients-header {
  margin-top: calc(88rpx + var(--status-bar-height, 44rpx));
  background: white;
  padding: 32rpx;
  border-bottom: 1rpx solid #eee;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ingredients-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.ingredients-text {
  font-size: 28rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recipe-count {
  font-size: 24rpx;
  color: #666;
}

/* 筛选标签 */
.filter-tags {
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.tags-scroll {
  white-space: nowrap;
}

.tags-list {
  display: flex;
  gap: 16rpx;
  padding: 0 32rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f0f0f0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.tag-item.active {
  background: #E8F5E8;
  color: #4CAF50;
}

/* 菜谱列表 */
.recipes-section {
  padding: 32rpx;
}

.recipes-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.recipe-card {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  transition: all 0.2s ease;
}

.recipe-card:active {
  transform: scale(0.98);
}

/* 菜谱图片 */
.recipe-image-container {
  position: relative;
  height: 320rpx;
}

.recipe-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent 50%, rgba(0,0,0,0.3));
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 24rpx;
}

.difficulty-badge,
.cook-time-badge {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background: rgba(0,0,0,0.6);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.difficulty-badge text,
.cook-time-badge text {
  font-size: 22rpx;
  color: white;
}

/* 菜谱信息 */
.recipe-info {
  padding: 32rpx;
  padding-bottom: 24rpx;
}

.recipe-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.recipe-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
  line-height: 1.3;
}

.recipe-rating {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
}

.recipe-description {
  margin-bottom: 20rpx;
}

.recipe-description text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 菜谱标签 */
.recipe-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.tag {
  padding: 6rpx 16rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #666;
}

/* 菜谱元信息 */
.recipe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.meta-item text {
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮 */
.recipe-actions {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255,255,255,0.9);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.9);
}

/* 空状态和加载状态 */
.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  padding: 40rpx;
}

.loading-state {
  gap: 20rpx;
}

.loading-state text {
  font-size: 28rpx;
  color: #666;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 20rpx;
}

.bottom-actions .van-button {
  flex: 1;
}

/* 筛选弹窗 */
.filter-modal {
  padding: 40rpx;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  margin-bottom: 40rpx;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.group-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.option-item {
  padding: 16rpx 24rpx;
  background: #f0f0f0;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.2s ease;
}

.option-item.active {
  background: #4CAF50;
  color: white;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
}

.filter-actions .van-button {
  flex: 1;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .recipe-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }
  
  .meta-item {
    width: 100%;
    justify-content: space-between;
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recipe-card {
  animation: slideInUp 0.3s ease;
}

.recipe-card:nth-child(2) {
  animation-delay: 0.1s;
}

.recipe-card:nth-child(3) {
  animation-delay: 0.2s;
}

/* 滚动优化 */
.tags-scroll {
  -webkit-overflow-scrolling: touch;
}

.filter-modal {
  -webkit-overflow-scrolling: touch;
}
