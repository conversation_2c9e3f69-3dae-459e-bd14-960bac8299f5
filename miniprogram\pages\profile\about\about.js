// 关于页面逻辑
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { get } from '../../../utils/api';

Page({
  data: {
    // 应用信息
    appVersion: '1.0.0',
    
    // 使用统计
    stats: {
      totalUsers: '10万+',
      totalRecipes: '50万+',
      totalRecognitions: '100万+',
      accuracy: '95%'
    },
    
    // 团队成员
    teamMembers: [
      {
        id: 1,
        name: '张三',
        role: '产品总监',
        avatar: '/images/avatar-default.png'
      },
      {
        id: 2,
        name: '李四',
        role: 'AI算法工程师',
        avatar: '/images/avatar-default.png'
      },
      {
        id: 3,
        name: '王五',
        role: '前端开发工程师',
        avatar: '/images/avatar-default.png'
      },
      {
        id: 4,
        name: '赵六',
        role: '后端开发工程师',
        avatar: '/images/avatar-default.png'
      }
    ],
    
    // 技术栈
    techStack: {
      frontend: ['微信小程序', 'Vant Weapp', 'JavaScript', 'WXSS'],
      backend: ['Node.js', 'Express', 'MySQL', 'Redis'],
      ai: ['TensorFlow', '计算机视觉', 'NLP', '深度学习']
    },
    
    // 更新日志
    changelog: [
      {
        version: '1.0.0',
        date: '2024-01-15',
        features: [
          '首次发布，支持拍照识别食材',
          'AI智能生成菜谱功能',
          '个人中心和收藏功能',
          '基础的用户系统'
        ]
      },
      {
        version: '0.9.0',
        date: '2024-01-01',
        features: [
          '内测版本发布',
          '基础的食材识别功能',
          '简单的菜谱推荐',
          '用户反馈收集'
        ]
      }
    ]
  },

  onLoad() {
    console.log('About page loaded');
    this.loadAppInfo();
    this.loadStats();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 加载应用信息
  loadAppInfo() {
    try {
      // 获取小程序版本信息
      const accountInfo = wx.getAccountInfoSync();
      if (accountInfo.miniProgram) {
        this.setData({
          appVersion: accountInfo.miniProgram.version || '1.0.0'
        });
      }
    } catch (error) {
      console.error('Load app info failed:', error);
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      const response = await get('/api/stats/app');
      if (response.success) {
        this.setData({
          stats: {
            totalUsers: this.formatNumber(response.data.totalUsers),
            totalRecipes: this.formatNumber(response.data.totalRecipes),
            totalRecognitions: this.formatNumber(response.data.totalRecognitions),
            accuracy: `${response.data.accuracy}%`
          }
        });
      }
    } catch (error) {
      console.error('Load stats failed:', error);
      // 使用默认数据
    }
  },

  // 格式化数字
  formatNumber(num) {
    if (num >= 100000) {
      return Math.floor(num / 10000) + '万+';
    } else if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  },

  // 复制联系方式
  copyContact(e) {
    const type = e.currentTarget.dataset.type;
    let content = '';
    let label = '';
    
    switch (type) {
      case 'email':
        content = '<EMAIL>';
        label = '邮箱';
        break;
      case 'wechat':
        content = 'airecipe_service';
        label = '微信号';
        break;
      case 'qq':
        content = '123456789';
        label = 'QQ群号';
        break;
    }
    
    if (content) {
      wx.setClipboardData({
        data: content,
        success: () => {
          Toast.success(`${label}已复制到剪贴板`);
        },
        fail: () => {
          Toast.fail('复制失败，请重试');
        }
      });
    }
  },

  // 打开隐私政策
  openPrivacyPolicy() {
    wx.navigateTo({
      url: '/pages/legal/privacy/privacy'
    });
  },

  // 打开用户协议
  openUserAgreement() {
    wx.navigateTo({
      url: '/pages/legal/agreement/agreement'
    });
  },

  // 打开版权声明
  openCopyright() {
    wx.showModal({
      title: '版权声明',
      content: `本应用所有内容，包括但不限于文字、图片、音频、视频、软件、程序、版面设计等均受著作权法保护。

未经授权，任何人不得复制、传播、展示、镜像、上载、下载使用本应用内容。

本应用中使用的第三方内容均已获得合法授权或符合相关法律法规要求。

如有版权问题，请联系我们：<EMAIL>`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 分享应用
  onShareAppMessage() {
    return {
      title: 'AI智能菜谱 - 让AI为你定制专属菜谱',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: 'AI智能菜谱 - 让AI为你定制专属菜谱',
      imageUrl: '/images/share-cover.png'
    };
  }
});
