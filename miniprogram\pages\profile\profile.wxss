/* 个人中心页面样式 */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 用户信息卡片 */
.user-card {
  margin-top: calc(88rpx + var(--status-bar-height, 44rpx) + 40rpx);
  margin-left: 32rpx;
  margin-right: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255,255,255,0.3);
  margin-right: 24rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-desc,
.login-tip {
  font-size: 26rpx;
  color: rgba(255,255,255,0.8);
}

.user-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 32rpx;
  border-top: 1rpx solid rgba(255,255,255,0.2);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255,255,255,0.8);
}

/* 功能菜单 */
.menu-section {
  margin: 40rpx 32rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.menu-group {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-extra {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.menu-count {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
}

/* 快捷操作 */
.quick-actions {
  margin: 0 32rpx 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
  transition: all 0.2s ease;
}

.action-item:active {
  transform: scale(0.95);
  background-color: #f8f8f8;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 最近活动 */
.recent-activity {
  margin: 0 32rpx 40rpx;
}

.activity-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:active {
  background-color: #f8f8f8;
}

.activity-icon {
  width: 48rpx;
  height: 48rpx;
  background: #f8f8f8;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.activity-text {
  font-size: 28rpx;
  color: #333;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 40rpx 32rpx;
  color: #999;
}

.version-text {
  display: block;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.copyright {
  font-size: 22rpx;
}

/* 登录弹窗 */
.login-modal {
  width: 600rpx;
  padding: 60rpx 40rpx 40rpx;
  background: white;
  border-radius: 20rpx;
}

.login-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.login-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
}

.login-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.login-desc {
  font-size: 28rpx;
  color: #666;
}

.login-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.login-tips {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

.login-tips .link {
  color: #4CAF50;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .user-stats {
    flex-wrap: wrap;
    gap: 24rpx;
  }
  
  .stat-item {
    flex: 1;
    min-width: 120rpx;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-card {
  animation: fadeInUp 0.5s ease;
}

.menu-group {
  animation: fadeInUp 0.5s ease;
}

.menu-group:nth-child(2) {
  animation-delay: 0.1s;
}

.menu-group:nth-child(3) {
  animation-delay: 0.2s;
}

.quick-actions {
  animation: fadeInUp 0.5s ease 0.3s both;
}

.recent-activity {
  animation: fadeInUp 0.5s ease 0.4s both;
}

/* 主题色变量 */
:root {
  --primary-color: #4CAF50;
  --secondary-color: #667eea;
  --accent-color: #764ba2;
  --text-primary: #333;
  --text-secondary: #666;
  --text-tertiary: #999;
  --background-primary: #f5f5f5;
  --background-secondary: white;
  --border-color: #eee;
  --shadow-light: 0 2rpx 12rpx rgba(0,0,0,0.05);
  --shadow-medium: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .container {
    background: #1a1a1a;
  }
  
  .custom-navbar {
    background: #2d2d2d;
    border-bottom-color: #444;
  }
  
  .navbar-title {
    color: #fff;
  }
  
  .menu-group,
  .activity-list {
    background: #2d2d2d;
  }
  
  .menu-text,
  .activity-text {
    color: #fff;
  }
  
  .menu-icon,
  .action-icon,
  .activity-icon {
    background: #444;
  }
  
  .section-title {
    color: #fff;
  }
}
